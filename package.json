{"name": "companywebsite", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^5.7.0", "@types/leaflet": "^1.9.17", "bcryptjs": "^2.4.3", "leaflet": "^1.9.4", "mysql2": "^3.6.5", "next": "^15.2.4", "next-auth": "^4.24.11", "next-intl": "^4.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-leaflet": "^4.2.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "postcss": "^8.5.3", "prisma": "^5.7.0", "tailwindcss": "^3.4.17", "typescript": "^5.3.3"}}