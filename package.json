{"name": "companywebsite", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup": "npm install fs-extra && node scripts/setup-tinymce.js", "deeplx:start": "node scripts/start-deeplx.js", "deeplx:stop": "node scripts/stop-deeplx.js", "test:translation": "node scripts/test-translation-integration.js", "test:deepl": "node scripts/test-deepl.js"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.0", "@prisma/client": "^5.7.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@tinymce/tinymce-react": "^6.2.1", "@types/leaflet": "^1.9.17", "@types/nodemailer": "^6.4.14", "antd": "^5.25.1", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "critters": "^0.0.25", "dotenv": "^16.5.0", "form-data": "^4.0.2", "framer-motion": "^12.6.3", "fs-extra": "^11.3.0", "i18next": "^24.2.3", "leaflet": "^1.9.4", "mysql2": "^3.6.5", "next": "^15.2.4", "next-auth": "^4.24.11", "next-intl": "^4.0.2", "nodemailer": "^6.9.13", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-leaflet": "^4.2.1", "react-quill": "^2.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "tinymce": "^7.0.0", "zod": "^3.25.56"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "postcss": "^8.5.3", "prisma": "^5.22.0", "tailwindcss": "^3.4.17", "typescript": "^5.3.3"}}