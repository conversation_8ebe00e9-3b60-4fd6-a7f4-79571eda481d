// 数据库连接检查脚本
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('=== 开始检查数据库 ===');
    
    // 测试数据库连接
    console.log('测试数据库连接...');
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ 数据库连接正常');
    
    // 检查用户表
    console.log('\n检查用户表...');
    const userCount = await prisma.user.count();
    console.log(`用户表中有 ${userCount} 个用户`);
    
    if (userCount > 0) {
      const users = await prisma.user.findMany({
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          name: true,
          phone: true
        }
      });
      console.log('用户列表:');
      console.table(users);
    }
    
    // 检查通知设置表
    console.log('\n检查通知设置表...');
    const notificationSettingCount = await prisma.notificationSetting.count();
    console.log(`通知设置表中有 ${notificationSettingCount} 条记录`);
    
    if (notificationSettingCount > 0) {
      const settings = await prisma.notificationSetting.findMany();
      console.log('通知设置:');
      console.table(settings);
    } else {
      console.log('⚠️ 通知设置表为空，这可能导致通知相关API出错');
    }
    
    // 检查留言表
    console.log('\n检查留言表...');
    const messageCount = await prisma.contactMessage.count();
    console.log(`留言表中有 ${messageCount} 条记录`);
    
    if (messageCount > 0) {
      const messages = await prisma.contactMessage.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' }
      });
      console.log('最近5条留言:');
      console.table(messages.map(m => ({
        id: m.id,
        name: m.name,
        email: m.email,
        subject: m.subject,
        status: m.status,
        createdAt: m.createdAt
      })));
    }
    
    console.log('\n=== 数据库检查完成 ===');
  } catch (error) {
    console.error('❌ 数据库检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
