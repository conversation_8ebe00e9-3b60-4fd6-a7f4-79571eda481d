#!/bin/bash

# 清理旧部署脚本
# 此脚本将删除旧的部署脚本，只保留新的部署脚本

echo "=== 开始清理旧部署脚本 ==="

# 要保留的新脚本
KEEP_FILES=(
  "deploy-full.sh"
  "deploy-update.sh"
  "DEPLOYMENT.md"
  "cleanup-old-scripts.sh"
)

# 要删除的旧脚本
OLD_FILES=(
  "deploy.sh"
  "clean-server.sh"
  "deploy-server-build.sh"
)

# 检查并删除旧脚本
for file in "${OLD_FILES[@]}"; do
  if [ -f "$file" ]; then
    echo "删除旧脚本: $file"
    rm "$file"
  else
    echo "未找到旧脚本: $file"
  fi
done

# 确认保留的新脚本存在
for file in "${KEEP_FILES[@]}"; do
  if [ -f "$file" ]; then
    echo "保留新脚本: $file"
    # 确保脚本有执行权限
    if [[ "$file" == *.sh ]]; then
      chmod +x "$file"
      echo "已为 $file 添加执行权限"
    fi
  else
    echo "警告: 未找到新脚本: $file"
  fi
done

echo "=== 清理完成 ==="
echo "现在只保留了新的部署脚本，避免混淆和潜在的错误。"
echo "请使用以下脚本进行部署:"
echo "- deploy-full.sh: 完全重新部署"
echo "- deploy-update.sh: 仅更新项目核心文件"
echo "详细说明请参考 DEPLOYMENT.md"
