// 创建管理员用户的脚本
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // 默认管理员用户名和密码
    const adminUsername = 'admin';
    const adminPassword = 'admin123';

    // 检查用户是否存在
    const existingUser = await prisma.user.findFirst({
      where: {
        username: adminUsername
      }
    });

    // 加密密码
    const hashedPassword = await bcrypt.hash(adminPassword, 10);

    if (existingUser) {
      // 更新现有用户的密码
      const updatedUser = await prisma.user.update({
        where: {
          id: existingUser.id
        },
        data: {
          password: hashedPassword
        }
      });
      console.log(\`已重置管理员密码，用户ID: \${updatedUser.id}\`);
    } else {
      // 创建新的管理员用户
      const newUser = await prisma.user.create({
        data: {
          username: adminUsername,
          password: hashedPassword,
          role: 'admin',
          email: '<EMAIL>'
        }
      });
      console.log(\`已创建新的管理员用户，用户ID: \${newUser.id}\`);
    }

    console.log('管理员用户名: admin');
    console.log('管理员密码: admin123');
  } catch (error) {
    console.error('创建/更新管理员用户时出错:', error);
  } finally {
    await prisma.\$disconnect();
  }
}

createAdminUser();
