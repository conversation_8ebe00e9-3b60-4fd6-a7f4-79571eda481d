const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  // 查询所有项目
  const projects = await prisma.project.findMany();
  console.log('Projects:', projects);
  console.log('Total projects:', projects.length);
  
  // 查询所有服务
  const services = await prisma.service.findMany();
  console.log('Services:', services);
  console.log('Total services:', services.length);
}

main()
  .catch(e => console.error(e))
  .finally(() => prisma.$disconnect());
