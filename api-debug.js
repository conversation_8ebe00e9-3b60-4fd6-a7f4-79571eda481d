// API调试脚本
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugProfileAPI() {
  try {
    console.log('=== 调试个人信息API ===');
    
    // 检查用户表
    console.log('检查用户表...');
    const userCount = await prisma.user.count();
    console.log(`用户表中有 ${userCount} 个用户`);
    
    if (userCount > 0) {
      const users = await prisma.user.findMany({
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          name: true,
          phone: true
        }
      });
      console.log('用户列表:');
      console.table(users);
    }
    
    // 模拟API逻辑
    console.log('\n模拟API逻辑...');
    const userId = 1; // 假设用户ID为1
    
    console.log(`获取用户信息，用户ID: ${userId}`);
    
    // 从数据库获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: parseInt(userId) },
      select: {
        id: true,
        name: true,
        username: true,
        email: true,
        phone: true,
        role: true,
      },
    });
    
    console.log('数据库返回的用户信息:', user);
    
    if (!user) {
      console.log('未找到用户');
    } else {
      console.log('API返回结果:', {
        success: true,
        ...user
      });
    }
  } catch (error) {
    console.error('调试个人信息API失败:', error);
  }
}

async function debugNotificationsAPI() {
  try {
    console.log('\n=== 调试通知设置API ===');
    
    // 检查通知设置表
    console.log('检查通知设置表...');
    const notificationSettingCount = await prisma.notificationSetting.count();
    console.log(`通知设置表中有 ${notificationSettingCount} 条记录`);
    
    if (notificationSettingCount > 0) {
      const settings = await prisma.notificationSetting.findMany();
      console.log('通知设置:');
      console.table(settings);
      
      console.log('API返回结果:', {
        success: true,
        settings: settings[0],
      });
    } else {
      console.log('通知设置表为空，尝试创建默认设置...');
      
      const defaultSettings = await prisma.notificationSetting.create({
        data: {
          enableWhatsApp: false,
          enableDingTalk: false,
          enableTelegram: false,
        }
      });
      
      console.log('已创建默认设置:', defaultSettings);
      
      console.log('API返回结果:', {
        success: true,
        settings: defaultSettings,
      });
    }
  } catch (error) {
    console.error('调试通知设置API失败:', error);
  }
}

async function debugMessagesAPI() {
  try {
    console.log('\n=== 调试留言列表API ===');
    
    // 检查留言表
    console.log('检查留言表...');
    const messageCount = await prisma.contactMessage.count();
    console.log(`留言表中有 ${messageCount} 条记录`);
    
    // 模拟API逻辑
    console.log('\n模拟API逻辑...');
    const page = 1;
    const pageSize = 10;
    const status = 'all';
    const locale = undefined;
    
    // 构建查询条件
    const where = {};
    
    if (status && status !== 'all') {
      where.status = status;
    }
    
    if (locale) {
      where.locale = locale;
    }
    
    // 查询留言
    const skip = (page - 1) * pageSize;
    
    const [messages, total] = await Promise.all([
      prisma.contactMessage.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: pageSize,
      }),
      prisma.contactMessage.count({ where }),
    ]);
    
    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);
    
    console.log(`查询结果: 共 ${total} 条留言，分 ${totalPages} 页`);
    
    if (messages.length > 0) {
      console.log('留言列表:');
      console.table(messages.map(m => ({
        id: m.id,
        name: m.name,
        email: m.email,
        subject: m.subject,
        status: m.status,
        createdAt: m.createdAt
      })));
    }
    
    console.log('API返回结果:', {
      messages,
      total,
      totalPages,
      currentPage: page,
    });
  } catch (error) {
    console.error('调试留言列表API失败:', error);
  }
}

async function runDebug() {
  try {
    console.log('=== 开始API调试 ===\n');
    
    await debugProfileAPI();
    await debugNotificationsAPI();
    await debugMessagesAPI();
    
    console.log('\n=== API调试完成 ===');
  } catch (error) {
    console.error('API调试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

runDebug();
