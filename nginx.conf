server {
    listen 80;
    server_name moco.top www.moco.top;

    # 日志配置
    access_log /var/log/nginx/companywebsite_access.log;
    error_log /var/log/nginx/companywebsite_error.log;

    # Next.js静态资源 - 允许长期缓存
    location /_next/ {
        alias /var/www/companywebsite/.next/;
        expires 365d;
        add_header Cache-Control "public, max-age=31536000, immutable";
        try_files $uri $uri/ =404;
        access_log off;
    }

    # 静态文件
    location /static/ {
        alias /var/www/companywebsite/public/static/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        try_files $uri $uri/ =404;
        access_log off;
    }

    # 上传文件目录
    location /uploads/ {
        alias /var/www/companywebsite/public/uploads/;
        try_files $uri =404;
    }

    # 其他静态文件
    location ~ ^/(favicon.ico|robots.txt|sitemap.xml)$ {
        root /var/www/companywebsite/public;
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
    }

    # 所有请求都转发到Next.js应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 增加超时时间
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
}
