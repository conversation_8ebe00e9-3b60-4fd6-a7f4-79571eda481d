#!/bin/bash

# 获取当前日期和时间
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 项目名称
PROJECT_NAME="companywebsite"

# 备份文件名
BACKUP_FILENAME="${PROJECT_NAME}_backup_${TIMESTAMP}.zip"

# 备份目录，默认为项目根目录的上一级目录下的backups文件夹
BACKUP_DIR="../backups"

# 创建备份目录（如果不存在）
mkdir -p "$BACKUP_DIR"

echo "开始备份项目..."

# 使用zip命令创建备份，排除不需要备份的文件夹
zip -r "$BACKUP_DIR/$BACKUP_FILENAME" . \
    -x "node_modules/*" \
    -x ".next/*" \
    -x ".git/*" \
    -x "backups/*" \
    -x "*.zip" \
    -x "*.log"

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo "备份成功！"
    echo "备份文件: $BACKUP_DIR/$BACKUP_FILENAME"
    echo "备份大小: $(du -h "$BACKUP_DIR/$BACKUP_FILENAME" | cut -f1)"
else
    echo "备份失败！"
    exit 1
fi
