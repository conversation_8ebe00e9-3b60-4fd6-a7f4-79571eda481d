# 公司网站部署指南

本文档提供了三种部署方式的详细说明：完全重新部署、仅更新项目核心文件，以及保留配置但迁移本地数据库的部署方式。

## 部署前准备

1. 确保本地项目已经正确配置
2. 确保服务器可以通过SSH访问
3. 确保您有服务器的root权限或sudo权限
4. 运行清理脚本删除旧的部署文件（首次使用新脚本时）：
   ```bash
   ./cleanup-old-scripts.sh
   ```

## 部署脚本说明

本项目提供了三个部署脚本：

1. `deploy-full.sh` - 完全重新部署脚本，会清理服务器并重新部署整个项目
2. `deploy-update.sh` - 更新部署脚本，只更新项目核心文件，保留数据库和配置
3. `deploy-with-db-migration.sh` - 保留配置但迁移本地数据库的部署脚本，删除服务器上的源代码和数据库，然后上传新的源代码并迁移本地数据库

## 1. 完全重新部署

当您需要在一个全新的服务器上部署项目，或者需要完全重置现有部署时，使用此方法。

### 完全重新部署会执行以下操作：

- 停止并删除所有相关服务
- 删除网站文件和Nginx配置
- 删除数据库和用户
- 上传并解压新的源代码
- 安装依赖并构建项目
- 创建新的数据库和用户
- 执行数据库迁移
- 创建管理员用户
- 配置Nginx
- 启动应用

### 使用方法：

```bash
# 给脚本添加执行权限
chmod +x deploy-full.sh

# 执行部署脚本
./deploy-full.sh ************* root
```

**注意：** 完全重新部署会删除服务器上的所有项目文件和数据库。请确保您已经备份了重要数据。

## 2. 仅更新项目核心文件

当您已经有一个正在运行的部署，只需要更新源代码而不影响数据库和配置时，使用此方法。

### 更新部署会执行以下操作：

- 备份关键文件
- 停止应用
- 保留数据目录和.env文件
- 删除旧的源代码文件
- 上传并解压新的源代码
- 恢复数据目录和.env文件
- 安装依赖并构建项目
- 应用新的数据库迁移（如果有）
- 启动应用

### 使用方法：

```bash
# 给脚本添加执行权限
chmod +x deploy-update.sh

# 执行更新脚本
./deploy-update.sh ************* root
```

## 3. 保留配置但迁移本地数据库

当您需要保留服务器上的配置（如Nginx设置、环境变量等），但希望用本地的最新源代码和数据库替换服务器上的内容时，使用此方法。

### 保留配置但迁移本地数据库的部署会执行以下操作：

- 导出本地数据库
- 备份服务器上的配置文件和上传的文件
- 停止应用
- 删除服务器上的源代码文件
- 重置服务器上的数据库
- 上传并解压新的源代码
- 恢复备份的配置和上传的文件
- 导入本地数据库到服务器
- 安装依赖并构建项目
- 启动应用

### 使用方法：

```bash
# 给脚本添加执行权限
chmod +x deploy-with-db-migration.sh

# 执行部署脚本
./deploy-with-db-migration.sh ************* root
```

**注意：** 此方法会提示您输入本地MySQL的用户名、密码和数据库名称，以便导出本地数据库。

## 部署后验证

无论使用哪种部署方法，部署完成后都应该验证网站是否正常运行：

1. 访问网站首页：http://moco.top
2. 访问管理后台：http://moco.top/admin/login
3. 检查各个功能是否正常工作

## 故障排除

如果部署过程中遇到问题，请检查以下日志文件：

1. Nginx错误日志：`/var/log/nginx/companywebsite_error.log`
2. PM2日志：使用 `pm2 logs companywebsite` 命令查看
3. 应用日志：在服务器上查看 `/var/www/companywebsite/logs` 目录（如果存在）

## 常见问题

### 1. 数据库连接错误

检查 `.env` 文件中的 `DATABASE_URL` 是否正确。默认配置为：

```
DATABASE_URL="mysql://prisma:prisma123@localhost:3306/companywebsite"
```

### 2. Nginx配置错误

检查Nginx配置文件：

```bash
nginx -t
```

如果有错误，修复后重启Nginx：

```bash
systemctl restart nginx
```

### 3. 应用无法启动

检查PM2状态：

```bash
pm2 status
```

如果应用未运行，尝试手动启动：

```bash
cd /var/www/companywebsite
pm2 start npm --name "companywebsite" -- start
```

## 部署摘要

- 服务器IP: *************
- 部署目录: /var/www/companywebsite
- 网站URL: http://moco.top
- 管理后台: http://moco.top/admin/login
- 管理员用户名: admin
- 管理员密码: admin123

## 安全建议

1. 部署完成后，建议更改管理员密码
2. 定期备份数据库和上传的文件
3. 定期更新服务器软件包
4. 考虑设置防火墙和SSL证书
