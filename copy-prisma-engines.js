// 复制Prisma引擎到standalone目录的脚本
const fs = require('fs');
const path = require('path');

// 源目录和目标目录
const sourceDir = path.join(__dirname, 'node_modules', '.prisma', 'client');
const targetDir = path.join(__dirname, '.next', 'standalone', 'node_modules', '.prisma', 'client');

// 确保目标目录存在
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`创建目录: ${dir}`);
  }
}

// 复制文件
function copyFile(source, target) {
  try {
    fs.copyFileSync(source, target);
    console.log(`复制文件: ${source} -> ${target}`);
  } catch (error) {
    console.error(`复制文件失败: ${source} -> ${target}`, error);
  }
}

// 复制目录
function copyDirectory(source, target) {
  ensureDirectoryExists(target);
  
  const files = fs.readdirSync(source);
  
  for (const file of files) {
    const sourcePath = path.join(source, file);
    const targetPath = path.join(target, file);
    
    const stats = fs.statSync(sourcePath);
    
    if (stats.isDirectory()) {
      copyDirectory(sourcePath, targetPath);
    } else {
      copyFile(sourcePath, targetPath);
    }
  }
}

// 主函数
function main() {
  console.log('开始复制Prisma引擎到standalone目录...');
  
  // 检查源目录是否存在
  if (!fs.existsSync(sourceDir)) {
    console.error(`源目录不存在: ${sourceDir}`);
    return;
  }
  
  // 检查.next/standalone目录是否存在
  const standaloneDir = path.join(__dirname, '.next', 'standalone');
  if (!fs.existsSync(standaloneDir)) {
    console.error(`standalone目录不存在: ${standaloneDir}`);
    console.log('请先构建项目: npm run build');
    return;
  }
  
  // 复制Prisma引擎
  ensureDirectoryExists(path.dirname(targetDir));
  copyDirectory(sourceDir, targetDir);
  
  console.log('Prisma引擎复制完成');
}

// 执行主函数
main();
