// 项目照片上传调试脚本
const fs = require('fs');
const path = require('path');

// 检查项目中的文件上传组件
function checkFileUploadComponent() {
  console.log('=== 检查项目中的文件上传组件 ===');
  
  // 检查项目创建页面中的文件上传组件
  const projectFormPath = path.join(process.cwd(), 'src', 'app', 'admin', 'projects', 'new', 'page.tsx');
  
  if (fs.existsSync(projectFormPath)) {
    console.log(`找到项目创建页面: ${projectFormPath}`);
    
    // 读取文件内容
    const content = fs.readFileSync(projectFormPath, 'utf8');
    
    // 检查fileInputRef的定义
    const fileInputRefMatch = content.match(/fileInputRef\s*=\s*useRef<HTMLInputElement>\(null\)/);
    if (fileInputRefMatch) {
      console.log('✅ 找到fileInputRef的定义');
    } else {
      console.error('❌ 未找到fileInputRef的定义');
    }
    
    // 检查handleImageUpload函数
    const handleImageUploadMatch = content.match(/const\s+handleImageUpload\s*=\s*async/);
    if (handleImageUploadMatch) {
      console.log('✅ 找到handleImageUpload函数');
    } else {
      console.error('❌ 未找到handleImageUpload函数');
    }
    
    // 检查triggerFileInput函数
    const triggerFileInputMatch = content.match(/const\s+triggerFileInput\s*=\s*\(\)\s*=>/);
    if (triggerFileInputMatch) {
      console.log('✅ 找到triggerFileInput函数');
    } else {
      console.error('❌ 未找到triggerFileInput函数');
    }
    
    // 检查上传按钮
    const uploadButtonMatch = content.match(/onClick\s*=\s*{\s*triggerFileInput\s*}/);
    if (uploadButtonMatch) {
      console.log('✅ 找到上传按钮');
    } else {
      console.error('❌ 未找到上传按钮');
    }
    
    // 检查文件输入框
    const fileInputMatch = content.match(/<input[^>]*type="file"[^>]*ref={fileInputRef}/);
    if (fileInputMatch) {
      console.log('✅ 找到文件输入框');
    } else {
      console.error('❌ 未找到文件输入框');
      
      // 检查是否有任何文件输入框
      const anyFileInputMatch = content.match(/<input[^>]*type="file"/);
      if (anyFileInputMatch) {
        console.log('⚠️ 找到文件输入框，但没有绑定到fileInputRef');
      }
    }
    
    // 检查是否有onChange处理函数
    const onChangeMatch = content.match(/onChange\s*=\s*{\s*handleImageUpload\s*}/);
    if (onChangeMatch) {
      console.log('✅ 找到onChange处理函数');
    } else {
      console.error('❌ 未找到onChange处理函数');
    }
    
    console.log('\n文件上传组件检查完成');
  } else {
    console.error(`❌ 未找到项目创建页面: ${projectFormPath}`);
  }
}

// 运行检查
checkFileUploadComponent();
