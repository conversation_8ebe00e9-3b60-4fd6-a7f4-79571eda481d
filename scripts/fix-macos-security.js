#!/usr/bin/env node

/**
 * 修复macOS安全警告的脚本
 * 移除node_modules中所有.node文件的隔离属性
 * 用法: node scripts/fix-macos-security.js
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 需要处理的文件模式
const FILE_PATTERNS = [
  '*.node',
  '*.dylib.node',
  '*swc*.node',
  '*query_engine*',
  '*schema_engine*',
  '*migration_engine*',
  '*introspection_engine*',
  '*prisma_fmt*',
  '*fsevents*.node'
];

// 查找所有需要处理的文件
function findFilesToFix() {
  return new Promise((resolve, reject) => {
    const files = [];
    let pendingSearches = FILE_PATTERNS.length;
    
    FILE_PATTERNS.forEach(pattern => {
      const findProcess = spawn('find', ['node_modules', '-name', pattern, '-type', 'f'], {
        cwd: process.cwd()
      });
      
      let output = '';
      findProcess.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      findProcess.on('close', (code) => {
        if (code === 0) {
          const foundFiles = output.trim().split('\n').filter(f => f.length > 0);
          files.push(...foundFiles);
        }
        
        pendingSearches--;
        if (pendingSearches === 0) {
          // 去重
          const uniqueFiles = [...new Set(files)];
          resolve(uniqueFiles);
        }
      });
      
      findProcess.on('error', (error) => {
        console.error(`查找文件时出错 (${pattern}):`, error.message);
        pendingSearches--;
        if (pendingSearches === 0) {
          resolve(files);
        }
      });
    });
  });
}

// 检查文件是否有隔离属性
function hasQuarantineAttribute(filePath) {
  return new Promise((resolve) => {
    const xattrProcess = spawn('xattr', ['-l', filePath]);
    let output = '';
    
    xattrProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    xattrProcess.on('close', (code) => {
      resolve(output.includes('com.apple.quarantine'));
    });
    
    xattrProcess.on('error', () => {
      resolve(false);
    });
  });
}

// 移除隔离属性
function removeQuarantineAttribute(filePath) {
  return new Promise((resolve, reject) => {
    const xattrProcess = spawn('xattr', ['-d', 'com.apple.quarantine', filePath]);
    
    xattrProcess.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`移除隔离属性失败，退出代码: ${code}`));
      }
    });
    
    xattrProcess.on('error', (error) => {
      reject(error);
    });
  });
}

// 处理单个文件
async function processFile(filePath) {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }
    
    // 检查是否有隔离属性
    const hasQuarantine = await hasQuarantineAttribute(filePath);
    
    if (hasQuarantine) {
      console.log(`🔧 处理文件: ${filePath}`);
      await removeQuarantineAttribute(filePath);
      console.log(`✅ 已移除隔离属性: ${filePath}`);
      return true;
    } else {
      console.log(`✓  无需处理: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
async function main() {
  console.log('=== macOS安全警告修复脚本 ===');
  console.log('正在查找需要处理的文件...\n');
  
  try {
    // 查找所有需要处理的文件
    const files = await findFilesToFix();
    
    if (files.length === 0) {
      console.log('✅ 未找到需要处理的文件');
      return;
    }
    
    console.log(`找到 ${files.length} 个文件需要检查:\n`);
    
    let processedCount = 0;
    let fixedCount = 0;
    
    // 处理每个文件
    for (const file of files) {
      const wasFixed = await processFile(file);
      processedCount++;
      if (wasFixed) {
        fixedCount++;
      }
    }
    
    console.log('\n=== 处理完成 ===');
    console.log(`检查文件数: ${processedCount}`);
    console.log(`修复文件数: ${fixedCount}`);
    
    if (fixedCount > 0) {
      console.log('\n✅ 已修复所有macOS安全警告!');
      console.log('现在可以重新启动开发服务器了。');
    } else {
      console.log('\n✓  所有文件都正常，无需修复。');
    }
    
  } catch (error) {
    console.error('❌ 脚本执行失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, findFilesToFix, processFile };
