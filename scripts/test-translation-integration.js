#!/usr/bin/env node

/**
 * 测试翻译功能集成的脚本
 * 用法: node scripts/test-translation-integration.js
 */

require('dotenv').config();

// 测试DeepLX本地服务
async function testDeepLX() {
  console.log('\n=== 测试DeepLX本地服务 ===');
  
  const apiUrl = process.env.DEEPLX_API_URL || 'http://127.0.0.1:1188/translate';
  const testText = '这是一个测试文本，用于验证DeepLX本地服务是否正常工作。';
  
  try {
    console.log('检查DeepLX服务状态...');
    const healthResponse = await fetch(apiUrl.replace('/translate', '/'), {
      signal: AbortSignal.timeout(3000)
    });
    
    if (!healthResponse.ok) {
      throw new Error(`服务健康检查失败: ${healthResponse.status}`);
    }
    
    console.log('✅ DeepLX服务运行正常');
    
    console.log('发送翻译请求...');
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text: testText,
        source_lang: 'zh',
        target_lang: 'en'
      }),
      signal: AbortSignal.timeout(10000)
    });
    
    console.log('响应状态:', response.status, response.statusText);
    
    if (!response.ok) {
      throw new Error(`DeepLX API错误: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log('响应数据:', JSON.stringify(data, null, 2));
    
    if (data.code === 200) {
      console.log('✅ DeepLX翻译成功!');
      console.log('原文:', testText);
      console.log('译文:', data.data);
      return true;
    } else {
      throw new Error('DeepLX返回错误代码: ' + data.code);
    }
  } catch (error) {
    console.error('❌ DeepLX测试失败:', error.message);
    return false;
  }
}

// 测试官方DeepL API
async function testOfficialDeepL() {
  console.log('\n=== 测试官方DeepL API ===');
  
  const apiKey = process.env.DEEPL_API_KEY;
  
  if (!apiKey) {
    console.error('❌ DeepL API密钥未配置');
    return false;
  }
  
  const testText = '这是一个测试文本，用于验证官方DeepL API是否正常工作。';
  
  try {
    console.log('发送翻译请求到官方API...');
    const response = await fetch('https://api-free.deepl.com/v2/translate', {
      method: 'POST',
      headers: {
        'Authorization': apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text: [testText],
        target_lang: 'EN',
        source_lang: 'ZH'
      }),
      signal: AbortSignal.timeout(15000)
    });
    
    console.log('响应状态:', response.status, response.statusText);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`官方DeepL API错误: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    const data = await response.json();
    console.log('响应数据:', JSON.stringify(data, null, 2));
    
    if (data && data.translations && data.translations[0]) {
      console.log('✅ 官方DeepL API翻译成功!');
      console.log('原文:', testText);
      console.log('译文:', data.translations[0].text);
      return true;
    } else {
      throw new Error('官方DeepL API返回数据格式错误');
    }
  } catch (error) {
    console.error('❌ 官方DeepL API测试失败:', error.message);
    return false;
  }
}

// 测试翻译服务API
async function testTranslationAPI() {
  console.log('\n=== 测试网站翻译API ===');
  
  const testText = '这是一个测试文本，用于验证网站翻译API是否正常工作。';
  
  try {
    console.log('发送翻译请求到网站API...');
    const response = await fetch('http://localhost:3000/api/admin/translate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text: testText,
        targetLang: 'en',
        sourceLang: 'zh'
      }),
      signal: AbortSignal.timeout(30000)
    });
    
    console.log('响应状态:', response.status, response.statusText);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`网站翻译API错误: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    const data = await response.json();
    console.log('响应数据:', JSON.stringify(data, null, 2));
    
    if (data.success && data.translatedText) {
      console.log('✅ 网站翻译API测试成功!');
      console.log('原文:', testText);
      console.log('译文:', data.translatedText);
      return true;
    } else {
      throw new Error('网站翻译API返回数据格式错误');
    }
  } catch (error) {
    console.error('❌ 网站翻译API测试失败:', error.message);
    console.log('注意: 请确保网站服务正在运行 (npm run dev)');
    return false;
  }
}

// 主函数
async function main() {
  console.log('=== 翻译功能集成测试 ===');
  console.log('测试时间:', new Date().toLocaleString());
  
  const results = {
    deeplx: false,
    official: false,
    api: false
  };
  
  // 测试DeepLX本地服务
  results.deeplx = await testDeepLX();
  
  // 测试官方DeepL API
  results.official = await testOfficialDeepL();
  
  // 测试网站翻译API
  results.api = await testTranslationAPI();
  
  // 总结结果
  console.log('\n=== 测试结果总结 ===');
  console.log('DeepLX本地服务:', results.deeplx ? '✅ 正常' : '❌ 失败');
  console.log('官方DeepL API:', results.official ? '✅ 正常' : '❌ 失败');
  console.log('网站翻译API:', results.api ? '✅ 正常' : '❌ 失败');
  
  if (results.deeplx || results.official) {
    console.log('\n✅ 翻译功能可用! 网站应该能够正常进行翻译。');
  } else {
    console.log('\n❌ 所有翻译服务都不可用，请检查配置。');
  }
  
  // 提供修复建议
  if (!results.deeplx && !results.official) {
    console.log('\n🔧 修复建议:');
    console.log('1. 检查DeepL API密钥是否正确配置');
    console.log('2. 尝试启动DeepLX服务: node scripts/start-deeplx.js');
    console.log('3. 检查网络连接是否正常');
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, testDeepLX, testOfficialDeepL, testTranslationAPI };
