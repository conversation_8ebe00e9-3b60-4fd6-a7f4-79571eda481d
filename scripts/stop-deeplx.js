#!/usr/bin/env node

/**
 * 停止DeepLX服务的脚本
 * 用法: node scripts/stop-deeplx.js
 */

const fs = require('fs');
const path = require('path');

// 检查服务是否运行
async function checkService() {
  try {
    const response = await fetch('http://127.0.0.1:1188/', {
      signal: AbortSignal.timeout(3000)
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

// 通过PID文件停止服务
function stopByPid() {
  const pidFile = path.join(__dirname, '../deeplx.pid');
  
  if (!fs.existsSync(pidFile)) {
    console.log('未找到PID文件，服务可能未通过脚本启动');
    return false;
  }

  try {
    const pid = parseInt(fs.readFileSync(pidFile, 'utf8').trim());
    console.log(`尝试停止进程 PID: ${pid}`);
    
    process.kill(pid, 'SIGTERM');
    
    // 删除PID文件
    fs.unlinkSync(pidFile);
    console.log('已发送停止信号');
    return true;
  } catch (error) {
    console.error('停止进程时出错:', error.message);
    
    // 如果进程不存在，删除PID文件
    if (error.code === 'ESRCH') {
      fs.unlinkSync(pidFile);
      console.log('进程已不存在，已清理PID文件');
    }
    return false;
  }
}

// 通过端口查找并停止进程
function stopByPort() {
  const { spawn } = require('child_process');
  
  return new Promise((resolve) => {
    // 在macOS上查找占用1188端口的进程
    const lsof = spawn('lsof', ['-ti:1188']);
    let pid = '';
    
    lsof.stdout.on('data', (data) => {
      pid += data.toString();
    });
    
    lsof.on('close', (code) => {
      if (code === 0 && pid.trim()) {
        const processId = parseInt(pid.trim());
        console.log(`发现占用端口1188的进程 PID: ${processId}`);
        
        try {
          process.kill(processId, 'SIGTERM');
          console.log('已发送停止信号');
          resolve(true);
        } catch (error) {
          console.error('停止进程时出错:', error.message);
          resolve(false);
        }
      } else {
        console.log('未找到占用端口1188的进程');
        resolve(false);
      }
    });
    
    lsof.on('error', (error) => {
      console.log('无法查找进程，可能是权限问题或lsof命令不可用');
      resolve(false);
    });
  });
}

// 主函数
async function main() {
  console.log('=== DeepLX服务停止脚本 ===');

  // 检查服务是否运行
  const isRunning = await checkService();
  if (!isRunning) {
    console.log('DeepLX服务未运行');
    return;
  }

  console.log('检测到DeepLX服务正在运行，正在停止...');

  // 首先尝试通过PID文件停止
  let stopped = stopByPid();
  
  // 如果PID方法失败，尝试通过端口查找
  if (!stopped) {
    console.log('尝试通过端口查找进程...');
    stopped = await stopByPort();
  }

  if (stopped) {
    // 等待服务停止
    console.log('等待服务停止...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const stillRunning = await checkService();
    if (!stillRunning) {
      console.log('✅ DeepLX服务已成功停止');
    } else {
      console.log('⚠️  服务可能仍在运行，请手动检查');
    }
  } else {
    console.log('❌ 无法停止服务，请手动检查并停止相关进程');
    console.log('您可以尝试运行: lsof -ti:1188 | xargs kill');
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, checkService };
