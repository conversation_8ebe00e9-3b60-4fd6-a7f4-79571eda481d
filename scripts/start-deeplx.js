#!/usr/bin/env node

/**
 * 启动DeepLX服务的脚本
 * 用法: node scripts/start-deeplx.js
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 检查Go是否安装
function checkGoInstallation() {
  return new Promise((resolve) => {
    const goVersion = spawn('go', ['version']);
    goVersion.on('close', (code) => {
      resolve(code === 0);
    });
    goVersion.on('error', () => {
      resolve(false);
    });
  });
}

// 检查DeepLX是否已编译
function checkDeepLXBinary() {
  const binaryPath = path.join(__dirname, '../DeepLX-main/deeplx');
  return fs.existsSync(binaryPath);
}

// 编译DeepLX
function buildDeepLX() {
  return new Promise((resolve, reject) => {
    console.log('正在编译DeepLX...');
    const buildProcess = spawn('go', ['build', '-o', 'deeplx', '.'], {
      cwd: path.join(__dirname, '../DeepLX-main'),
      stdio: 'inherit'
    });

    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log('DeepLX编译成功!');
        resolve();
      } else {
        reject(new Error(`编译失败，退出代码: ${code}`));
      }
    });

    buildProcess.on('error', (error) => {
      reject(error);
    });
  });
}

// 启动DeepLX服务
function startDeepLX() {
  return new Promise((resolve, reject) => {
    console.log('正在启动DeepLX服务...');
    const deeplxPath = path.join(__dirname, '../DeepLX-main/deeplx');
    
    const deeplxProcess = spawn(deeplxPath, ['-p', '1188'], {
      stdio: 'inherit',
      detached: true
    });

    // 等待一段时间确保服务启动
    setTimeout(() => {
      console.log('DeepLX服务已启动，监听端口: 1188');
      console.log('进程ID:', deeplxProcess.pid);
      
      // 保存进程ID到文件
      fs.writeFileSync(path.join(__dirname, '../deeplx.pid'), deeplxProcess.pid.toString());
      
      // 分离进程，让它在后台运行
      deeplxProcess.unref();
      resolve(deeplxProcess);
    }, 2000);

    deeplxProcess.on('error', (error) => {
      reject(error);
    });
  });
}

// 检查服务是否运行
async function checkService() {
  try {
    const response = await fetch('http://127.0.0.1:1188/', {
      signal: AbortSignal.timeout(3000)
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

// 主函数
async function main() {
  console.log('=== DeepLX服务启动脚本 ===');

  // 检查服务是否已经运行
  const isRunning = await checkService();
  if (isRunning) {
    console.log('DeepLX服务已经在运行中!');
    return;
  }

  // 检查Go环境
  const hasGo = await checkGoInstallation();
  if (!hasGo) {
    console.error('错误: 未找到Go环境，请先安装Go语言环境');
    console.log('安装指南: https://golang.org/doc/install');
    process.exit(1);
  }

  console.log('Go环境检查通过');

  try {
    // 检查是否需要编译
    if (!checkDeepLXBinary()) {
      await buildDeepLX();
    } else {
      console.log('发现已编译的DeepLX二进制文件');
    }

    // 启动服务
    await startDeepLX();
    
    // 验证服务启动
    console.log('等待服务启动...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const serviceRunning = await checkService();
    if (serviceRunning) {
      console.log('✅ DeepLX服务启动成功!');
      console.log('服务地址: http://127.0.0.1:1188');
      console.log('要停止服务，请运行: node scripts/stop-deeplx.js');
    } else {
      console.error('❌ 服务启动失败，请检查日志');
    }

  } catch (error) {
    console.error('启动DeepLX服务时出错:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, checkService };
