#!/usr/bin/env node

/**
 * 简单的翻译功能测试脚本
 * 直接测试翻译服务，不需要认证
 * 用法: node scripts/test-translation-simple.js
 */

require('dotenv').config();

// 导入翻译服务
const path = require('path');
const { translateText, translateObject } = require('../src/lib/services/translationService.ts');

// 测试翻译文本
async function testTranslateText() {
  console.log('\n=== 测试文本翻译 ===');
  
  const testCases = [
    { text: '你好世界', targetLang: 'en', sourceLang: 'zh' },
    { text: 'Hello World', targetLang: 'zh', sourceLang: 'en' },
    { text: '这是一个测试', targetLang: 'ar', sourceLang: 'zh' }
  ];
  
  for (const testCase of testCases) {
    try {
      console.log(`\n测试: ${testCase.text} (${testCase.sourceLang} -> ${testCase.targetLang})`);
      
      const result = await translateText(
        testCase.text,
        testCase.targetLang,
        testCase.sourceLang
      );
      
      console.log(`✅ 翻译成功: ${result}`);
    } catch (error) {
      console.error(`❌ 翻译失败:`, error.message);
    }
  }
}

// 测试翻译对象
async function testTranslateObject() {
  console.log('\n=== 测试对象翻译 ===');
  
  const testObject = {
    title: '公司简介',
    description: '我们是一家专业的工程公司',
    services: ['设计', '施工', '咨询'],
    contact: {
      address: '北京市朝阳区',
      phone: '010-12345678'
    }
  };
  
  try {
    console.log('\n原始对象:', JSON.stringify(testObject, null, 2));
    
    const result = await translateObject(testObject, 'en', 'zh');
    
    console.log('\n✅ 翻译结果:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('❌ 对象翻译失败:', error.message);
  }
}

// 测试HTML翻译
async function testTranslateHTML() {
  console.log('\n=== 测试HTML翻译 ===');
  
  const htmlContent = '<h1>欢迎访问我们的网站</h1><p>我们提供<strong>专业的服务</strong>。</p>';
  
  try {
    console.log('\n原始HTML:', htmlContent);
    
    const result = await translateText(htmlContent, 'en', 'zh');
    
    console.log('\n✅ HTML翻译结果:', result);
  } catch (error) {
    console.error('❌ HTML翻译失败:', error.message);
  }
}

// 主函数
async function main() {
  console.log('=== 翻译功能简单测试 ===');
  console.log('测试时间:', new Date().toLocaleString());
  
  // 检查环境变量
  console.log('\n=== 环境配置检查 ===');
  console.log('DEEPL_API_KEY:', process.env.DEEPL_API_KEY ? '已配置' : '未配置');
  console.log('DEEPLX_API_URL:', process.env.DEEPLX_API_URL || '使用默认值');
  
  try {
    // 测试文本翻译
    await testTranslateText();
    
    // 测试对象翻译
    await testTranslateObject();
    
    // 测试HTML翻译
    await testTranslateHTML();
    
    console.log('\n=== 测试完成 ===');
    console.log('✅ 翻译功能测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, testTranslateText, testTranslateObject, testTranslateHTML };
