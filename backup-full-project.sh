#!/bin/bash

# 获取当前日期和时间
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 项目名称
PROJECT_NAME="companywebsite"

# 备份文件名
BACKUP_FILENAME="${PROJECT_NAME}_full_backup_${TIMESTAMP}.tar.gz"

# 备份目录，默认为~/pengyang/backups
BACKUP_DIR=~/pengyang/backups

# 创建备份目录（如果不存在）
mkdir -p "$BACKUP_DIR"

echo "开始完整备份项目..."
echo "这将包括所有文件和目录，包括node_modules和.next"

# 使用tar命令创建完整备份
tar -czf "$BACKUP_DIR/$BACKUP_FILENAME" .

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo "备份成功！"
    echo "备份文件: $BACKUP_DIR/$BACKUP_FILENAME"
    echo "备份大小: $(du -h "$BACKUP_DIR/$BACKUP_FILENAME" | cut -f1)"
else
    echo "备份失败！"
    exit 1
fi
