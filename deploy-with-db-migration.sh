#!/bin/bash

# 部署脚本 - 删除服务器上的源代码和数据库，保留配置，并迁移本地数据库
# 使用方法: ./deploy-with-db-migration.sh [服务器IP] [用户名]
# 例如: ./deploy-with-db-migration.sh ************* root

# 错误处理函数
handle_error() {
  local exit_code=$1
  local error_message=$2
  local line_number=$3

  echo "错误: $error_message (行号: $line_number, 退出码: $exit_code)"
  echo "部署失败。请修复错误后重试。"
  exit $exit_code
}

# 设置错误处理
trap 'handle_error $? "命令执行失败" $LINENO' ERR

# 设置严格模式
set -e

# 检查参数
if [ $# -lt 2 ]; then
  echo "用法: ./deploy-with-db-migration.sh ************* root"
  echo "例如: ./deploy-with-db-migration.sh 123.456.789.0 root"
  exit 1
fi

SERVER_IP=$1
USERNAME=$2
REMOTE_DIR="/var/www/companywebsite"
LOCAL_DB_DUMP="local_db_dump.sql"

echo "=== 部署公司网站到阿里云服务器（保留配置，迁移数据库）==="
echo "服务器IP: $SERVER_IP"
echo "用户名: $USERNAME"
echo "远程目录: $REMOTE_DIR"

# 确认操作
read -p "警告: 此操作将删除服务器上的所有源代码文件和数据库，但保留配置。确定要继续吗? (y/n): " CONFIRM
if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
  echo "操作已取消"
  exit 0
fi

# 第1步: 导出本地数据库或使用现有备份
echo "=== 第1步: 准备数据库备份 ==="
read -p "是否使用现有的数据库备份文件? (y/n): " USE_EXISTING_BACKUP

if [ "$USE_EXISTING_BACKUP" = "y" ] || [ "$USE_EXISTING_BACKUP" = "Y" ]; then
  read -p "请输入现有数据库备份文件的路径: " EXISTING_BACKUP_PATH
  if [ -f "$EXISTING_BACKUP_PATH" ]; then
    echo "使用现有备份文件: $EXISTING_BACKUP_PATH"
    if [ "$EXISTING_BACKUP_PATH" != "$LOCAL_DB_DUMP" ]; then
      cp "$EXISTING_BACKUP_PATH" $LOCAL_DB_DUMP
    fi
  else
    echo "错误: 指定的备份文件不存在"
    exit 1
  fi
else
  read -p "请输入本地MySQL用户名: " LOCAL_MYSQL_USER
  read -s -p "请输入本地MySQL密码: " LOCAL_MYSQL_PASS
  echo ""
  read -p "请输入本地数据库名称 [companywebsite]: " LOCAL_DB_NAME
  LOCAL_DB_NAME=${LOCAL_DB_NAME:-companywebsite}

  echo "正在导出本地数据库..."
  if [ -z "$LOCAL_MYSQL_PASS" ]; then
    mysqldump -u $LOCAL_MYSQL_USER $LOCAL_DB_NAME > $LOCAL_DB_DUMP
  else
    mysqldump -u $LOCAL_MYSQL_USER -p"$LOCAL_MYSQL_PASS" $LOCAL_DB_NAME > $LOCAL_DB_DUMP
  fi

  if [ ! -f "$LOCAL_DB_DUMP" ]; then
    echo "导出本地数据库失败，请检查数据库连接信息"
    exit 1
  fi

  echo "本地数据库已导出到 $LOCAL_DB_DUMP"
fi

# 第2步: 备份服务器配置
echo "=== 第2步: 备份服务器配置 ==="
ssh -t $USERNAME@$SERVER_IP << 'EOF'
  echo "=== 备份服务器配置 ==="
  mkdir -p /tmp/config_backup

  # 备份.env文件
  if [ -f "$REMOTE_DIR/.env" ]; then
    cp $REMOTE_DIR/.env /tmp/config_backup/
    echo ".env文件已备份"
  fi

  # 备份Nginx配置
  if [ -f "/etc/nginx/sites-available/companywebsite" ]; then
    cp /etc/nginx/sites-available/companywebsite /tmp/config_backup/
    echo "Nginx配置已备份"
  fi

  # 备份上传的文件
  if [ -d "$REMOTE_DIR/public/uploads" ]; then
    mkdir -p /tmp/config_backup/uploads
    cp -r $REMOTE_DIR/public/uploads/* /tmp/config_backup/uploads/ 2>/dev/null || true
    echo "上传的文件已备份"
  fi
EOF

# 第3步: 清理服务器
echo "=== 第3步: 清理服务器 ==="
ssh -t $USERNAME@$SERVER_IP << 'EOF'
  echo "=== 停止所有相关服务 ==="
  # 停止PM2进程
  pm2 delete companywebsite 2>/dev/null || true
  pm2 save 2>/dev/null || true

  # 停止Nginx
  systemctl stop nginx || true

  echo "=== 删除网站文件 ==="
  # 删除网站目录，但保留备份的配置
  if [ -d "$REMOTE_DIR" ]; then
    find $REMOTE_DIR -mindepth 1 -not -path "$REMOTE_DIR/public/uploads*" -delete
    echo "网站文件已删除（保留上传目录）"
  fi

  echo "=== 清理数据库 ==="
  # 删除数据库
  mysql -u root << MYSQL_SCRIPT
DROP DATABASE IF EXISTS companywebsite;
CREATE DATABASE companywebsite CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON companywebsite.* TO 'prisma'@'localhost';
FLUSH PRIVILEGES;
MYSQL_SCRIPT
  echo "数据库已重置"

  echo "=== 清理临时文件 ==="
  # 清理临时文件
  rm -rf /tmp/deploy-src.tar.gz
EOF

# 第4步: 创建源代码包
echo "=== 第4步: 创建源代码包 ==="
mkdir -p deploy-src
cp -r src deploy-src/
cp -r public deploy-src/
cp -r prisma deploy-src/
cp -r messages deploy-src/ 2>/dev/null || true
cp -r data deploy-src/ 2>/dev/null || true
cp package.json deploy-src/
cp package-lock.json deploy-src/
cp next.config.js deploy-src/
cp .env deploy-src/
cp .env.local deploy-src/ 2>/dev/null || true
cp tsconfig.json deploy-src/
cp postcss.config.js deploy-src/ 2>/dev/null || true
cp postcss.config.mjs deploy-src/ 2>/dev/null || true
cp tailwind.config.js deploy-src/ 2>/dev/null || true
cp tailwind.config.ts deploy-src/ 2>/dev/null || true
cp middleware.ts deploy-src/ 2>/dev/null || true
cp i18n.config.ts deploy-src/ 2>/dev/null || true
cp next-env.d.ts deploy-src/ 2>/dev/null || true
cp eslint.config.mjs deploy-src/ 2>/dev/null || true

# 压缩源代码包
echo "=== 压缩源代码包 ==="
tar -czf deploy-src.tar.gz -C deploy-src .
rm -rf deploy-src

# 第5步: 上传到服务器
echo "=== 第5步: 上传到服务器 ==="
scp deploy-src.tar.gz $LOCAL_DB_DUMP $USERNAME@$SERVER_IP:/tmp/

# 第6步: 在服务器上执行部署
echo "=== 第6步: 在服务器上执行部署 ==="
ssh -t $USERNAME@$SERVER_IP << EOF
  # 创建新的目录（如果不存在）
  mkdir -p $REMOTE_DIR

  # 解压源代码包
  echo "=== 解压源代码包 ==="
  tar -xzf /tmp/deploy-src.tar.gz -C $REMOTE_DIR

  # 恢复备份的配置
  echo "=== 恢复备份的配置 ==="
  if [ -f "/tmp/config_backup/.env" ]; then
    cp /tmp/config_backup/.env $REMOTE_DIR/
    echo ".env文件已恢复"
  fi

  if [ -f "/tmp/config_backup/companywebsite" ]; then
    cp /tmp/config_backup/companywebsite /etc/nginx/sites-available/
    ln -sf /etc/nginx/sites-available/companywebsite /etc/nginx/sites-enabled/
    echo "Nginx配置已恢复"
  fi

  if [ -d "/tmp/config_backup/uploads" ]; then
    mkdir -p $REMOTE_DIR/public/uploads
    cp -r /tmp/config_backup/uploads/* $REMOTE_DIR/public/uploads/ 2>/dev/null || true
    echo "上传的文件已恢复"
  fi

  # 导入本地数据库
  echo "=== 导入本地数据库 ==="
  mysql -u root companywebsite < /tmp/$LOCAL_DB_DUMP
  echo "本地数据库已导入"

  # 安装依赖
  echo "=== 安装依赖 ==="
  cd $REMOTE_DIR
  npm install

  # 生成Prisma客户端
  echo "=== 生成Prisma客户端 ==="
  npx prisma generate

  # 构建项目
  echo "=== 构建项目 ==="
  npm run build

  # 设置文件权限
  echo "=== 设置文件权限 ==="
  chmod -R 755 $REMOTE_DIR

  # 设置上传目录的特殊权限
  if [ -d "$REMOTE_DIR/public/uploads" ]; then
    chmod -R 777 $REMOTE_DIR/public/uploads
    echo "已设置上传目录权限"
  else
    mkdir -p $REMOTE_DIR/public/uploads
    chmod -R 777 $REMOTE_DIR/public/uploads
    echo "已创建并设置上传目录权限"
  fi

  # 设置.env文件的权限
  chmod 600 $REMOTE_DIR/.env

  # 重启Nginx
  systemctl restart nginx

  # 启动应用
  echo "=== 启动应用 ==="
  pm2 start npm --name "companywebsite" -- start
  pm2 save

  # 清理临时文件
  echo "=== 清理临时文件 ==="
  rm -f /tmp/deploy-src.tar.gz
  rm -f /tmp/$LOCAL_DB_DUMP
  rm -rf /tmp/config_backup
EOF

# 清理本地文件
echo "=== 清理本地文件 ==="
rm deploy-src.tar.gz
rm $LOCAL_DB_DUMP

echo "=== 部署完成 ==="
echo ""
echo "部署摘要:"
echo "- 服务器IP: $SERVER_IP"
echo "- 部署目录: $REMOTE_DIR"
echo "- 网站URL: http://moco.top"
echo "- 管理后台: http://moco.top/admin/login"
echo ""
echo "如果您遇到任何问题，请检查以下内容:"
echo "1. Nginx日志: /var/log/nginx/companywebsite_error.log"
echo "2. PM2日志: 使用 'pm2 logs companywebsite' 命令查看"
echo ""
echo "您可以使用以下命令连接到服务器:"
echo "ssh $USERNAME@$SERVER_IP"
