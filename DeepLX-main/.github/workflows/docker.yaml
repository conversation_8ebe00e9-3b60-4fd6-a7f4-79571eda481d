name: Docker Image CI

on:
  push:
    branches:
      - dev
    tags:
      - 'v*'

env:
  DOCKER_IMAGE_NAME: missuo/deeplx
  GHCR_IMAGE_NAME: ${{ github.repository }}
  DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
  DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
  GHCR_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  GHCR_USERNAME: ${{ github.repository_owner }}

jobs:
  docker_build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
        with:
          platforms: all

      - name: Set up docker buildx
        id: buildx
        uses: docker/setup-buildx-action@v2
        with:
          version: latest

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          registry: docker.io
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ env.DOCKER_PASSWORD }}

      - name: Login to GHCR
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ env.GHCR_USERNAME }}
          password: ${{ env.GHCR_TOKEN }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          # list of Docker images to use as base name for tags
          images: |
            docker.io/${{ env.DOCKER_IMAGE_NAME }}
            ghcr.io/${{ env.GHCR_IMAGE_NAME }}
          # generate Docker tags based on the following events/attributes
          tags: |
            type=raw,value=latest,enable=${{ startsWith(github.ref, 'refs/tags/') }}
            type=pep440,pattern={{raw}},enable=${{ startsWith(github.ref, 'refs/tags/') }}
            type=raw,value=dev,enable=${{ github.ref == 'refs/heads/dev' }}

      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
