on:
  push:
  pull_request:

name: CI

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.24.2'

      - name: Install golint
        run: go install golang.org/x/lint/golint@latest

      - name: Build
        run: go build ./...

      - name: Test
        run: go test ./...
