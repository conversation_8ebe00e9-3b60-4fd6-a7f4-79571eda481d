on:
  push:
    tags:
      - 'v*'
  pull_request:

name: Release
jobs:
        
  Build:
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v4
        with:
          go-version: "1.24.2"

      - run: bash .cross_compile.sh

      - name: Release
        uses: softprops/action-gh-release@v1
        with:
          draft: false
          generate_release_notes: true
          files: |
            dist/*
