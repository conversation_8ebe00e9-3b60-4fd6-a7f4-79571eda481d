#!/bin/bash

# 清理缓存脚本
# 使用方法: ./clear-cache.sh [服务器IP] [用户名]
# 例如: ./clear-cache.sh 123.456.789.0 root

# 检查参数
if [ $# -lt 2 ]; then
  echo "用法: ./clear-cache.sh [服务器IP] [用户名]"
  echo "例如: ./clear-cache.sh 123.456.789.0 root"
  exit 1
fi

SERVER_IP=$1
USERNAME=$2

echo "=== 开始清理服务器缓存 ==="
echo "服务器IP: $SERVER_IP"
echo "用户名: $USERNAME"

# 在服务器上执行清理操作
ssh -t $USERNAME@$SERVER_IP << EOF
  echo "=== 清理Nginx缓存 ==="
  rm -rf /var/cache/nginx/* || true
  
  echo "=== 重启Nginx服务 ==="
  systemctl restart nginx || true
  
  echo "=== 重启Next.js应用 ==="
  export NVM_DIR="/home/<USER>/.nvm"
  [ -s "\$NVM_DIR/nvm.sh" ] && \. "\$NVM_DIR/nvm.sh"
  pm2 restart companywebsite || true
  
  echo "=== 检查服务状态 ==="
  systemctl status nginx | grep Active
  pm2 status companywebsite
EOF

echo "=== 缓存清理完成 ==="
echo "请刷新浏览器并清除浏览器缓存以查看更改"
echo "浏览器清除缓存方法:"
echo "Chrome: Ctrl+Shift+Delete"
echo "Firefox: Ctrl+Shift+Delete"
echo "Safari: Option+Command+E"
echo "Edge: Ctrl+Shift+Delete"
