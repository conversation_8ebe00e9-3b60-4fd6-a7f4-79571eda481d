#!/bin/bash

# 修复数据库连接脚本
# 使用方法: ./fix-db-connection.sh [服务器IP] [用户名]
# 例如: ./fix-db-connection.sh ************* root

# 检查参数
if [ $# -lt 2 ]; then
  echo "用法: ./fix-db-connection.sh ************* root"
  exit 1
fi

SERVER_IP=$1
USERNAME=$2
REMOTE_DIR="/var/www/companywebsite"

echo "=== 修复数据库连接 ==="
echo "服务器IP: $SERVER_IP"
echo "用户名: $USERNAME"
echo "远程目录: $REMOTE_DIR"

# 确认操作
read -p "确定要修复数据库连接吗? (y/n): " CONFIRM
if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
  echo "操作已取消"
  exit 0
fi

# 在服务器上执行修复
ssh -t $USERNAME@$SERVER_IP << EOF
  # 备份当前.env文件
  echo "备份当前.env文件..."
  cp $REMOTE_DIR/.env $REMOTE_DIR/.env.bak
  
  # 更新数据库连接信息
  echo "更新数据库连接信息..."
  sed -i 's/DATABASE_URL=.*$/DATABASE_URL="mysql:\/\/prisma:prisma123@localhost:3306\/companywebsite"/' $REMOTE_DIR/.env
  
  # 显示更新后的.env文件
  echo "更新后的数据库连接信息:"
  grep DATABASE_URL $REMOTE_DIR/.env
  
  # 重启应用
  echo "重启应用..."
  pm2 restart companywebsite
  
  echo "修复完成"
EOF

echo "=== 修复完成 ==="
echo "请检查网站是否正常工作"
