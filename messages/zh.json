{"home": {"title": "MOCO Engineering", "description": "专业的化工与石化工程设计、采购、施工(EPC)解决方案提供商", "nav": {"home": "首页", "about": "关于我们", "services": "服务", "projects": "项目案例", "contact": "联系我们"}, "hero": {"title": "专业的工程解决方案", "content": "MOCO Engineering 是一家专业的化工与石化工程设计、采购、施工(EPC)解决方案提供商，致力于为客户提供高质量、高效率的一站式服务。", "learnMore": "了解更多", "contactUs": "联系我们"}, "services": {"title": "我们的服务", "subtitle": "我们提供全方位的工程服务，从设计到采购再到施工，确保项目的顺利进行。", "engineering": {"title": "工程设计", "description": "提供专业的化工与石化工程设计服务，包括概念设计、基础设计和详细设计。"}, "procurement": {"title": "设备采购", "description": "协助客户采购高质量的设备和材料，确保项目的质量和进度。"}, "construction": {"title": "工程施工", "description": "提供专业的施工服务，确保项目按时、按质、按量完成。"}}, "footer": {"description": "MOCO Engineering 是一家专业的化工与石化工程设计、采购、施工(EPC)解决方案提供商。", "quickLinks": "快速链接", "address": "中国上海市浦东新区张江高科技园区", "rights": "版权所有", "copyright": "版权所有", "followUs": "关注我们", "companyDescription": "MOCO Engineering 是一家专业的化工与石化工程设计、采购、施工(EPC)解决方案提供商，致力于为客户提供高质量、高效率的一站式服务。"}}, "about": {"title": "关于我们", "description": "了解更多关于MOCO Engineering的信息", "companyOverview": "公司概况", "mission": "我们的使命", "vision": "我们的愿景", "values": "核心价值观", "history": "公司历史", "team": "我们的团队", "achievements": "公司成就", "enterCompanyOverview": "请输入公司概况..."}, "services": {"title": "我们的服务", "description": "我们提供全方位的化工与石化工程解决方案，包括工程设计、设备采购和工程施工。", "items": [{"id": "engineering", "title": "工程设计", "description": "提供专业的化工与石化工程设计服务，包括概念设计、基础设计和详细设计。", "features": ["概念设计", "基础设计", "详细设计", "工艺流程图", "设备布置图", "管道仪表图"]}, {"id": "procurement", "title": "设备采购", "description": "协助客户采购高质量的设备和材料，确保项目的质量和进度。", "features": ["供应商评估", "设备规格制定", "招标管理", "合同谈判", "质量控制", "物流管理"]}, {"id": "construction", "title": "工程施工", "description": "提供专业的施工服务，确保项目按时、按质、按量完成。", "features": ["施工计划", "现场管理", "质量控制", "安全管理", "进度控制", "成本控制"]}], "consultation": {"title": "需要更多信息？", "description": "联系我们，了解更多关于我们服务的详细信息。", "button": "联系我们"}}, "projects": {"title": "项目案例", "description": "我们的团队已成功完成了多个化工与石化工程项目，以下是部分案例展示。", "filters": {"all": "全部", "chemical": "化工", "petrochemical": "石化", "refinery": "炼油"}, "loading": "加载中...", "noProjects": "没有找到匹配的项目。", "viewDetails": "查看详情", "errorMessage": "获取项目数据失败，请稍后重试", "retry": "重试"}, "contact": {"title": "联系我们", "description": "如果您有任何问题或需求，请随时与我们联系。", "form": {"name": "姓名", "email": "电子邮箱", "phone": "电话", "message": "留言", "submit": "提交", "success": "您的信息已成功提交，我们将尽快与您联系。", "error": "提交失败，请稍后重试。"}, "info": {"address": "地址", "phone": "电话", "email": "电子邮箱", "workingHours": "工作时间"}, "map": {"loading": "地图加载中..."}}, "admin": {"login": {"title": "管理员登录", "username": "用户名", "password": "密码", "submit": "登录", "error": "用户名或密码错误"}, "dashboard": {"title": "管理员仪表盘", "stats": "网站统计", "content": "内容管理", "settings": "系统设置", "logout": "退出登录", "viewSite": "访问网站"}}, "Talent": {"title": "人才计划", "description": "加入我们的团队，共同创造未来", "applicationStatus": "申请状态查询", "applicationStatusDescription": "请输入您的申请编号和联系信息（邮箱或手机号）查询申请状态。", "applicationId": "申请编号", "pleaseEnterApplicationId": "请输入申请编号", "enterApplicationId": "输入申请编号", "email": "电子邮箱", "pleaseEnterEmailOrPhone": "请输入邮箱或手机号", "enterEmail": "输入电子邮箱", "phone": "手机号码", "enterPhone": "输入手机号码", "queryStatus": "查询状态", "queryFailed": "查询失败", "querying": "正在查询...", "applicationResult": "申请结果", "congratulations": "恭喜！您的申请已被接受。", "applicationRejected": "很遗憾，您的申请未被接受。", "applicationInProgress": "您的申请正在处理中，请耐心等待。", "applicationSubmitted": "申请已提交", "applicationReviewed": "申请已审核", "applicationAccepted": "申请已接受", "applicationDetails": "申请详情", "name": "姓名", "position": "申请职位", "program": "申请计划", "status": "申请状态", "applicationDate": "申请日期", "backToTalentPage": "返回人才计划页面"}}