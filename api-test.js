// API测试脚本
const axios = require('axios');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 创建一个带Cookie的axios实例
const axiosInstance = axios.create({
  baseURL: 'http://localhost:3001',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 保存和加载Cookie
const cookieJarPath = path.join(__dirname, 'cookie.json');

function saveCookies(cookies) {
  fs.writeFileSync(cookieJarPath, JSON.stringify(cookies));
}

function loadCookies() {
  if (fs.existsSync(cookieJarPath)) {
    return JSON.parse(fs.readFileSync(cookieJarPath, 'utf8'));
  }
  return null;
}

// 登录函数
async function login(username, password) {
  console.log(`\n尝试登录 (${username})...`);
  try {
    // NextAuth登录API
    const response = await axiosInstance.post('/api/auth/signin/credentials', {
      username,
      password,
      redirect: false,
      callbackUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/admin/dashboard`,
      json: true
    });

    console.log(`✅ 登录成功，状态码: ${response.status}`);

    // 保存Cookie
    if (response.headers['set-cookie']) {
      saveCookies(response.headers['set-cookie']);
      axiosInstance.defaults.headers.Cookie = response.headers['set-cookie'].join('; ');
    }

    return true;
  } catch (error) {
    console.error(`❌ 登录失败: ${error.message}`);
    if (error.response) {
      console.error(`状态码: ${error.response.status}`);
      console.error('响应数据:', error.response.data);
    }
    return false;
  }
}

async function testAPI(url, description) {
  console.log(`\n测试 ${description} (${url})...`);
  try {
    const response = await axiosInstance.get(url);
    console.log(`✅ 状态码: ${response.status}`);
    console.log('响应数据:', JSON.stringify(response.data, null, 2).substring(0, 500) + '...');
    return true;
  } catch (error) {
    console.error(`❌ 错误: ${error.message}`);
    if (error.response) {
      console.error(`状态码: ${error.response.status}`);
      console.error('响应数据:', error.response.data);
    }
    return false;
  }
}

async function runTests() {
  console.log('=== 开始API测试 ===');

  // 直接测试API接口，不进行登录
  console.log('\n注意: 请先在浏览器中登录后台管理系统，然后再运行此测试');

  // 测试profile接口
  await testAPI('/api/admin/profile', '个人信息接口');

  // 测试notifications接口
  await testAPI('/api/admin/settings/notifications', '通知设置接口');

  // 测试messages接口
  await testAPI('/api/admin/messages', '留言列表接口');

  console.log('\n=== API测试完成 ===');
}

runTests();
