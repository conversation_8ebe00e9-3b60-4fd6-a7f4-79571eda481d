# 翻译功能使用指南

本项目集成了DeepLX离线翻译服务和官方DeepL API，提供智能翻译功能。

## 功能特性

- **智能翻译**: 优先使用DeepLX本地服务，失败时自动回退到官方DeepL API
- **离线翻译**: 支持DeepLX本地部署，减少API调用成本
- **多语言支持**: 支持中文、英文、阿拉伯文之间的翻译
- **HTML翻译**: 支持带HTML标签的内容翻译
- **速率限制**: 内置API调用频率控制
- **错误处理**: 完善的错误处理和重试机制

## 环境配置

### 1. DeepL API密钥配置

在 `.env` 文件中配置官方DeepL API密钥：

```env
# DeepL API密钥 (格式: "DeepL-Auth-Key {YOUR_API_KEY}")
DEEPL_API_KEY=DeepL-Auth-Key your-api-key-here:fx

# DeepLX本地服务地址 (可选)
DEEPLX_API_URL=http://127.0.0.1:1188/translate
```

### 2. 获取DeepL API密钥

1. 访问 [DeepL API官网](https://www.deepl.com/pro-api)
2. 注册账户并获取免费API密钥
3. 将密钥配置到环境变量中

## DeepLX本地服务

### 安装Go环境

DeepLX需要Go语言环境来编译运行：

```bash
# macOS (使用Homebrew)
brew install go

# 或者从官网下载安装
# https://golang.org/doc/install
```

### 启动DeepLX服务

```bash
# 启动DeepLX本地服务
npm run deeplx:start

# 或者直接运行脚本
node scripts/start-deeplx.js
```

### 停止DeepLX服务

```bash
# 停止DeepLX服务
npm run deeplx:stop

# 或者直接运行脚本
node scripts/stop-deeplx.js
```

## 测试翻译功能

### 运行集成测试

```bash
# 测试所有翻译功能
npm run test:translation

# 测试官方DeepL API
npm run test:deepl
```

### 手动测试

```bash
# 测试DeepLX本地服务
curl -X POST http://127.0.0.1:1188/translate \
  -H "Content-Type: application/json" \
  -d '{"text":"你好世界","source_lang":"zh","target_lang":"en"}'

# 测试网站翻译API
curl -X POST http://localhost:3000/api/admin/translate \
  -H "Content-Type: application/json" \
  -d '{"text":"你好世界","targetLang":"en","sourceLang":"zh"}'
```

## 使用方法

### 在后台管理系统中使用

1. 登录后台管理系统
2. 编辑内容时，输入中文内容
3. 点击"翻译并保存"按钮
4. 系统会自动翻译内容到其他语言

### 在代码中使用

```javascript
import { translateText, translateObject } from '@/lib/services/translationService';

// 翻译文本
const translatedText = await translateText('你好世界', 'en', 'zh');

// 翻译对象
const translatedObject = await translateObject({
  title: '标题',
  content: '内容'
}, 'en', 'zh');
```

## 故障排除

### 常见问题

1. **DeepLX服务启动失败**
   - 检查Go环境是否正确安装
   - 确保端口1188未被占用
   - 查看错误日志

2. **官方API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 检查API配额是否用完

3. **翻译结果为空**
   - 检查源语言和目标语言设置
   - 确认输入文本不为空
   - 查看控制台错误信息

### 日志查看

翻译过程中的详细日志会输出到控制台，包括：
- 翻译服务选择
- API调用状态
- 错误信息
- 翻译结果

### 性能优化

1. **优先使用DeepLX**: 本地服务响应更快，无API调用限制
2. **批量翻译**: 对于大量文本，系统会自动进行批量处理
3. **缓存机制**: 相同文本的翻译结果会被缓存

## Docker部署

如果您更喜欢使用Docker部署：

```bash
# 启动完整服务栈
docker-compose up -d

# 仅启动DeepLX服务
docker-compose up -d deeplx
```

## 支持的语言

- **中文** (zh): 简体中文
- **英文** (en): 英语
- **阿拉伯文** (ar): 阿拉伯语

## API接口

### 翻译API

**POST** `/api/admin/translate`

请求体：
```json
{
  "text": "要翻译的文本",
  "targetLang": "en",
  "sourceLang": "zh"
}
```

响应：
```json
{
  "success": true,
  "translatedText": "Translated text"
}
```

## 更新日志

- **v1.0**: 集成DeepLX本地服务
- **v1.1**: 添加智能回退机制
- **v1.2**: 优化错误处理和日志
- **v1.3**: 添加批量翻译支持

## 技术支持

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 运行测试脚本检查服务状态
3. 查看控制台日志获取详细错误信息
