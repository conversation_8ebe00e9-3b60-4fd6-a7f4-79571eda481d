#!/bin/bash

# 完全重新部署脚本 - 清理服务器并完全重新部署项目
# 使用方法: ./deploy-full.sh [服务器IP] [用户名]
# 例如: ./deploy-full.sh ************* root

# 错误处理函数
handle_error() {
  local exit_code=$1
  local error_message=$2
  local line_number=$3

  echo "错误: $error_message (行号: $line_number, 退出码: $exit_code)"
  echo "部署失败。请修复错误后重试。"
  exit $exit_code
}

# 设置错误处理
trap 'handle_error $? "命令执行失败" $LINENO' ERR

# 设置严格模式
set -e

# 检查参数
if [ $# -lt 2 ]; then
  echo "用法: ./deploy-full.sh ************* root"
  echo "例如: ./deploy-full.sh 123.456.789.0 root"
  exit 1
fi

SERVER_IP=$1
USERNAME=$2
REMOTE_DIR="/var/www/companywebsite"

echo "=== 完全重新部署公司网站到阿里云服务器 ==="
echo "服务器IP: $SERVER_IP"
echo "用户名: $USERNAME"
echo "远程目录: $REMOTE_DIR"

# 确认操作
read -p "警告: 此操作将删除服务器上的所有项目文件和数据库。确定要继续吗? (y/n): " CONFIRM
if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
  echo "操作已取消"
  exit 0
fi

# 第1步: 清理服务器
echo "=== 第1步: 清理服务器 ==="
ssh -t $USERNAME@$SERVER_IP << 'EOF'
  echo "=== 停止所有相关服务 ==="
  # 停止PM2进程
  pm2 delete companywebsite 2>/dev/null || true
  pm2 delete all 2>/dev/null || true
  pm2 save 2>/dev/null || true
  
  # 停止Nginx
  systemctl stop nginx || true
  
  echo "=== 删除网站文件 ==="
  # 删除网站目录
  rm -rf /var/www/companywebsite
  
  echo "=== 删除Nginx配置 ==="
  # 删除Nginx配置
  rm -f /etc/nginx/sites-available/companywebsite
  rm -f /etc/nginx/sites-enabled/companywebsite
  
  # 恢复默认Nginx配置
  if [ -f /etc/nginx/sites-available/default ]; then
    ln -sf /etc/nginx/sites-available/default /etc/nginx/sites-enabled/
  fi
  
  echo "=== 清理数据库 ==="
  # 备份数据库（以防万一）
  BACKUP_FILE="/tmp/companywebsite_backup_before_clean_$(date +%Y%m%d_%H%M%S).sql"
  mysqldump -u root companywebsite > $BACKUP_FILE 2>/dev/null || true
  echo "数据库已备份到 $BACKUP_FILE"
  
  # 删除数据库
  mysql -u root << MYSQL_SCRIPT
DROP DATABASE IF EXISTS companywebsite;
DROP USER IF EXISTS 'companyuser'@'localhost';
DROP USER IF EXISTS 'prisma'@'localhost';
FLUSH PRIVILEGES;
MYSQL_SCRIPT
  echo "数据库和用户已删除"
  
  echo "=== 清理临时文件 ==="
  # 清理临时文件
  rm -rf /tmp/deploy-src.tar.gz
  rm -rf /tmp/site_backup_*
  rm -rf /tmp/companywebsite_db_backup_*
  rm -rf /tmp/backup
  rm -f /tmp/rollback.sh
  
  echo "=== 清理Nginx缓存 ==="
  # 清理Nginx缓存
  rm -rf /var/cache/nginx/*
EOF

# 第2步: 创建源代码包
echo "=== 第2步: 创建源代码包 ==="
mkdir -p deploy-src
cp -r src deploy-src/
cp -r public deploy-src/
cp -r prisma deploy-src/
cp -r messages deploy-src/ 2>/dev/null || true
cp -r data deploy-src/ 2>/dev/null || true
cp package.json deploy-src/
cp package-lock.json deploy-src/
cp next.config.js deploy-src/
cp .env deploy-src/
cp .env.local deploy-src/ 2>/dev/null || true
cp tsconfig.json deploy-src/
cp postcss.config.js deploy-src/ 2>/dev/null || true
cp postcss.config.mjs deploy-src/ 2>/dev/null || true
cp tailwind.config.js deploy-src/ 2>/dev/null || true
cp tailwind.config.ts deploy-src/ 2>/dev/null || true
cp middleware.ts deploy-src/ 2>/dev/null || true
cp i18n.config.ts deploy-src/ 2>/dev/null || true
cp next-env.d.ts deploy-src/ 2>/dev/null || true
cp eslint.config.mjs deploy-src/ 2>/dev/null || true

# 压缩源代码包
echo "=== 压缩源代码包 ==="
tar -czf deploy-src.tar.gz -C deploy-src .
rm -rf deploy-src

# 第3步: 上传到服务器
echo "=== 第3步: 上传到服务器 ==="
scp deploy-src.tar.gz $USERNAME@$SERVER_IP:/tmp/

# 第4步: 在服务器上执行部署
echo "=== 第4步: 在服务器上执行部署 ==="
ssh -t $USERNAME@$SERVER_IP << EOF
  # 检查服务器环境
  echo "=== 检查服务器环境 ==="
  
  # 检查Node.js
  if ! command -v node &> /dev/null; then
    echo "服务器未安装Node.js，正在安装..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
  fi
  
  # 检查npm
  if ! command -v npm &> /dev/null; then
    echo "服务器未安装npm，正在安装..."
    apt-get install -y npm
  fi
  
  # 检查PM2
  if ! command -v pm2 &> /dev/null; then
    echo "服务器未安装PM2，正在安装..."
    npm install -g pm2
  fi
  
  # 检查Nginx
  if ! command -v nginx &> /dev/null; then
    echo "服务器未安装Nginx，正在安装..."
    apt-get update
    apt-get install -y nginx
  fi
  
  # 检查MySQL
  if ! command -v mysql &> /dev/null; then
    echo "服务器未安装MySQL，正在安装..."
    apt-get update
    apt-get install -y mysql-server
    
    # 启动MySQL服务
    systemctl start mysql
    systemctl enable mysql
    
    # 设置MySQL安全配置
    echo "设置MySQL安全配置..."
    mysql_secure_installation <<MYSQL_SETUP
y
1
Company123!
Company123!
y
y
y
y
MYSQL_SETUP
  fi
  
  # 检查MySQL数据库和用户
  echo "创建MySQL数据库和用户..."
  mysql -u root <<MYSQL_SCRIPT
CREATE DATABASE IF NOT EXISTS companywebsite CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'prisma'@'localhost' IDENTIFIED BY 'prisma123';
GRANT ALL PRIVILEGES ON companywebsite.* TO 'prisma'@'localhost';
FLUSH PRIVILEGES;
MYSQL_SCRIPT
  echo "MySQL数据库和用户已创建"
  
  # 创建新的目录
  mkdir -p $REMOTE_DIR
  
  # 解压源代码包
  echo "=== 解压源代码包 ==="
  tar -xzf /tmp/deploy-src.tar.gz -C $REMOTE_DIR
  
  # 安装依赖
  echo "=== 安装依赖 ==="
  cd $REMOTE_DIR
  npm install
  
  # 检查.env文件中的数据库连接字符串
  if ! grep -q "DATABASE_URL" $REMOTE_DIR/.env; then
    echo "DATABASE_URL=\"mysql://prisma:prisma123@localhost:3306/companywebsite\"" >> $REMOTE_DIR/.env
    echo "已添加DATABASE_URL到.env文件"
  else
    # 更新数据库连接字符串
    sed -i 's|DATABASE_URL=.*|DATABASE_URL="mysql://prisma:prisma123@localhost:3306/companywebsite"|g' $REMOTE_DIR/.env
    echo "已更新DATABASE_URL"
  fi
  
  # 检查.env文件中的NEXTAUTH_URL
  if ! grep -q "NEXTAUTH_URL" $REMOTE_DIR/.env; then
    echo "NEXTAUTH_URL=http://moco.top" >> $REMOTE_DIR/.env
    echo "已添加NEXTAUTH_URL到.env文件"
  fi
  
  # 检查.env文件中的NEXTAUTH_SECRET
  if ! grep -q "NEXTAUTH_SECRET" $REMOTE_DIR/.env; then
    echo "NEXTAUTH_SECRET=qmw/qBozQQT2tCQ1ZU5K8pccGPjG6l2IlLIcpRq1GZM=" >> $REMOTE_DIR/.env
    echo "已添加NEXTAUTH_SECRET到.env文件"
  fi
  
  # 生成Prisma客户端
  echo "=== 生成Prisma客户端 ==="
  npx prisma generate
  
  # 执行数据库迁移
  echo "=== 执行数据库迁移 ==="
  npx prisma migrate deploy
  
  # 构建项目
  echo "=== 构建项目 ==="
  npm run build
  
  # 设置文件权限
  echo "=== 设置文件权限 ==="
  chmod -R 755 $REMOTE_DIR
  
  # 设置上传目录的特殊权限
  if [ -d "$REMOTE_DIR/public/uploads" ]; then
    chmod -R 777 $REMOTE_DIR/public/uploads
    echo "已设置上传目录权限"
  else
    mkdir -p $REMOTE_DIR/public/uploads
    chmod -R 777 $REMOTE_DIR/public/uploads
    echo "已创建并设置上传目录权限"
  fi
  
  # 设置.env文件的权限
  chmod 600 $REMOTE_DIR/.env
  
  # 创建Nginx配置
  echo "=== 创建Nginx配置 ==="
  cat > /etc/nginx/sites-available/companywebsite << 'NGINX_CONFIG'
server {
    listen 80;
    server_name moco.top www.moco.top;

    access_log /var/log/nginx/companywebsite_access.log;
    error_log /var/log/nginx/companywebsite_error.log;

    # 静态资源
    location /_next/static/ {
        alias /var/www/companywebsite/.next/static/;
        expires 365d;
        add_header Cache-Control "public, max-age=31536000, immutable";
        add_header Access-Control-Allow-Origin "*" always;
    }

    # 上传文件
    location /uploads/ {
        alias /var/www/companywebsite/public/uploads/;
        add_header Access-Control-Allow-Origin "*" always;
    }

    # 静态文件
    location ~ ^/(favicon.ico|robots.txt)$ {
        root /var/www/companywebsite/public;
        expires 7d;
    }

    # 代理到Next.js应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
    }
}
NGINX_CONFIG
  
  # 确保Nginx配置链接正确
  ln -sf /etc/nginx/sites-available/companywebsite /etc/nginx/sites-enabled/
  rm -f /etc/nginx/sites-enabled/default
  
  # 测试Nginx配置
  nginx -t
  
  # 重启Nginx
  systemctl restart nginx
  
  # 创建管理员用户
  echo "=== 创建管理员用户 ==="
  cd $REMOTE_DIR
  cat > create-admin.js << 'ADMIN_SCRIPT'
// 创建管理员用户的脚本
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // 默认管理员用户名和密码
    const adminUsername = 'admin';
    const adminPassword = 'admin123';
    
    // 加密密码
    const hashedPassword = await bcrypt.hash(adminPassword, 10);

    // 创建新的管理员用户
    const newUser = await prisma.user.create({
      data: {
        username: adminUsername,
        password: hashedPassword,
        role: 'admin',
        email: '<EMAIL>'
      }
    });
    console.log('已创建新的管理员用户，用户ID: ' + newUser.id);
    console.log('管理员用户名: admin');
    console.log('管理员密码: admin123');
  } catch (error) {
    console.error('创建管理员用户时出错:', error);
  } finally {
    await prisma.\$disconnect();
  }
}

createAdminUser();
ADMIN_SCRIPT
  
  node create-admin.js
  
  # 启动应用
  echo "=== 启动应用 ==="
  pm2 start npm --name "companywebsite" -- start
  pm2 save
  pm2 startup
  
  # 清理临时文件
  echo "=== 清理临时文件 ==="
  rm -f /tmp/deploy-src.tar.gz
EOF

# 清理本地文件
echo "=== 清理本地文件 ==="
rm deploy-src.tar.gz

echo "=== 部署完成 ==="
echo ""
echo "部署摘要:"
echo "- 服务器IP: $SERVER_IP"
echo "- 部署目录: $REMOTE_DIR"
echo "- 网站URL: http://moco.top"
echo "- 管理后台: http://moco.top/admin/login"
echo "- 管理员用户名: admin"
echo "- 管理员密码: admin123"
echo ""
echo "如果您遇到任何问题，请检查以下内容:"
echo "1. Nginx日志: /var/log/nginx/companywebsite_error.log"
echo "2. PM2日志: 使用 'pm2 logs companywebsite' 命令查看"
echo ""
echo "您可以使用以下命令连接到服务器:"
echo "ssh $USERNAME@$SERVER_IP"
