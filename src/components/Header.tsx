import Link from 'next/link'

export default function Header({ lang }: { lang: string }) {
  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link href={`/${lang}`} className="text-2xl font-bold text-blue-800">
          MOCO Engineering
        </Link>
        <nav className="flex gap-6">
          <Link href={`/${lang}/about`} className="hover:text-blue-600">关于我们</Link>
          <Link href={`/${lang}/services`} className="hover:text-blue-600">服务项目</Link>
          <Link href={`/${lang}/projects`} className="hover:text-blue-600">工程案例</Link>
          <Link href={`/${lang}/contact`} className="hover:text-blue-600">联系我们</Link>
        </nav>
      </div>
    </header>
  )
}