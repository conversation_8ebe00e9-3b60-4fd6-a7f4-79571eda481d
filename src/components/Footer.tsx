'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';

interface ContactData {
  address: string;
  phone: string;
  email: string;
  workingHours: string;
  socialMedia: {
    weibo: string;
    wechat: string;
    linkedin: string;
  };
}

export default function Footer({ locale, t }: { locale: string, t: any }) {
  const [contactData, setContactData] = useState<ContactData | null>(null);
  // 由于loading状态未被使用，可以直接移除这一行
  
  useEffect(() => {
    const fetchContactData = async () => {
      try {
        const response = await fetch('/api/admin/contact');
        if (response.ok) {
          const data = await response.json();
          setContactData(data);
        }
      } catch (error) {
        console.error('获取联系方式数据失败:', error);
      } finally {
// 由于loading状态已被移除，这里不需要设置loading状态，可以直接删除这行代码
      }
    };
    
    fetchContactData();
  }, []);
  
  return (
    <footer className="bg-gray-800 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* 公司信息 */}
          <div>
            <h3 className="text-xl font-bold mb-4">{t.footer.companyName}</h3>
            <p className="mb-4">{t.footer.companyDescription}</p>
            {contactData && (
              <div className="space-y-2">
                {contactData.address && (
                  <p className="flex items-start">
                    <span className="mr-2">📍</span>
                    <span>{contactData.address}</span>
                  </p>
                )}
                {contactData.phone && (
                  <p className="flex items-start">
                    <span className="mr-2">📞</span>
                    <span>{contactData.phone}</span>
                  </p>
                )}
                {contactData.email && (
                  <p className="flex items-start">
                    <span className="mr-2">✉️</span>
                    <span>{contactData.email}</span>
                  </p>
                )}
              </div>
            )}
          </div>
          
          {/* 快速链接 */}
          <div>
            <h3 className="text-xl font-bold mb-4">{t.footer.quickLinks}</h3>
            <ul className="space-y-2">
              <li>
                <Link href={`/${locale}`} className="hover:text-blue-300 transition">
                  {t.nav.home}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/about`} className="hover:text-blue-300 transition">
                  {t.nav.about}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/services`} className="hover:text-blue-300 transition">
                  {t.nav.services}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/projects`} className="hover:text-blue-300 transition">
                  {t.nav.projects}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/contact`} className="hover:text-blue-300 transition">
                  {t.nav.contact}
                </Link>
              </li>
            </ul>
          </div>
          
          {/* 服务 */}
          <div>
            <h3 className="text-xl font-bold mb-4">{t.footer.services}</h3>
            <ul className="space-y-2">
              {t.services.items.slice(0, 5).map((service: any, index: number) => (
                <li key={index}>
                  <Link href={`/${locale}/services#${service.id}`} className="hover:text-blue-300 transition">
                    {service.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          {/* 社交媒体 */}
          <div>
            <h3 className="text-xl font-bold mb-4">{t.footer.followUs}</h3>
            {contactData && contactData.socialMedia && (
              <div className="flex space-x-4">
                {contactData.socialMedia.weibo && (
                  <a href={contactData.socialMedia.weibo} target="_blank" rel="noopener noreferrer" className="text-white hover:text-blue-300 transition">
                    <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9.82727273,24 C9.82727273,24 15.7364532,23.8153846 17.9708943,19.6307692 C19.9127273,15.8461538 14.7018182,6.46153846 5.44727273,10.1538462 C5.44727273,10.1538462 2.42545455,11.1076923 4.1490909,13.1538462 C4.1490909,13.1538462 5.05090909,12.3076923 5.82727273,11.6923077 C5.82727273,11.6923077 6.01090909,11.5384615 5.34545455,12.4615385 C5.34545455,12.4615385 2.63272727,16.4615385 8.02909091,16.6153846 C8.02909091,16.6153846 9.27272727,14.4615385 10.0363636,13.7692308 C10.0363636,13.7692308 10.2509091,13.5384615 10.0363636,13.8461538 C10.0363636,13.8461538 8.10909091,16.9230769 9.82727273,17.0769231 C9.82727273,17.0769231 12.4854545,13.3846154 13.1090909,12.8461538 C13.1090909,12.8461538 13.3236364,12.6923077 13.1090909,13 C13.1090909,13 11.0581818,16.0769231 13.1090909,16.2307692 C13.1090909,16.2307692 18.2181818,8.15384615 9.82727273,7.07692308 C9.82727273,7.07692308 5.44727273,6.15384615 5.44727273,8.15384615 C5.44727273,8.15384615 19.4545455,2.61538462 9.82727273,0 C9.82727273,0 7.34545455,0.153846154 6.47272727,0.769230769 C6.47272727,0.769230769 6.01090909,0.923076923 6.47272727,0.615384615 C6.47272727,0.615384615 7.34545455,0 9.82727273,0 C9.82727273,0 20.3272727,1.84615385 20.3272727,12.4615385 C20.3272727,12.4615385 21.2,20.7692308 9.82727273,24 Z"></path>
                    </svg>
                  </a>
                )}
                {contactData.socialMedia.wechat && (
                  <a href="#" className="text-white hover:text-blue-300 transition" title={contactData.socialMedia.wechat}>
                    <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8.69,11.52c-0.59,0-1.21,0.54-1.21,1.21c0,0.66,0.62,1.21,1.21,1.21s1.21-0.54,1.21-1.21C9.9,12.06,9.28,11.52,8.69,11.52z M12,0C5.37,0,0,5.37,0,12c0,6.63,5.37,12,12,12c6.63,0,12-5.37,12-12C24,5.37,18.63,0,12,0z M16.54,16.3c-0.71,0.53-1.62,0.9-2.57,1.05c-0.95,0.15-1.95,0.07-2.86-0.23c-0.91-0.3-1.73-0.84-2.36-1.54c-0.63-0.69-1.07-1.53-1.24-2.42c-0.18-0.89-0.07-1.84,0.29-2.67c0.36-0.83,0.98-1.55,1.74-2.09c0.77-0.54,1.68-0.9,2.63-1.04c0.95-0.15,1.95-0.07,2.86,0.23c0.91,0.3,1.72,0.84,2.36,1.54c0.63,0.69,1.07,1.53,1.24,2.42c0.18,0.89,0.07,1.84-0.29,2.67C17.92,15.05,17.3,15.77,16.54,16.3z M15.31,11.52c-0.59,0-1.21,0.54-1.21,1.21c0,0.66,0.62,1.21,1.21,1.21c0.59,0,1.21-0.54,1.21-1.21C16.52,12.06,15.9,11.52,15.31,11.52z"></path>
                    </svg>
                  </a>
                )}
                {contactData.socialMedia.linkedin && (
                  <a href={contactData.socialMedia.linkedin} target="_blank" rel="noopener noreferrer" className="text-white hover:text-blue-300 transition">
                    <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                    </svg>
                  </a>
                )}
              </div>
            )}
            
            {contactData && contactData.workingHours && (
              <div className="mt-4">
                <h4 className="font-semibold mb-2">{t.footer.workingHours}</h4>
                <p>{contactData.workingHours}</p>
              </div>
            )}
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-8 pt-8 text-center">
          <p>© {new Date().getFullYear()} {t.footer.companyName}. {t.footer.allRightsReserved}</p>
        </div>
      </div>
    </footer>
  );
}