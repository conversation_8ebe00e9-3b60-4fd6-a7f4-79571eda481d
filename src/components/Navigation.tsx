import Link from 'next/link'

export default function Navigation({ lang }: { lang: string }) {
  const navigation = [
    { name: 'about', href: `/${lang}/about` },
    { name: 'products', href: `/${lang}/products` },
    { name: 'services', href: `/${lang}/services` },
    { name: 'contact', href: `/${lang}/contact` },
  ]

  return (
    <nav>
      <ul className="flex space-x-8">
        {navigation.map((item) => (
          <li key={item.name}>
            <Link href={item.href} className="hover:text-blue-600">
              {item.name}
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  )
}