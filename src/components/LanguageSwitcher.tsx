import { i18n } from '@/i18n-config'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function LanguageSwitcher({ currentLang }: { currentLang: string }) {
  const pathname = usePathname()
  const redirectedPathname = (locale: string) => {
    if (!pathname) return '/'
    
    // 如果是管理员路径，不进行语言切换
    if (pathname.startsWith('/admin')) {
      return pathname
    }
    
    const segments = pathname.split('/')
    segments[1] = locale
    return segments.join('/')
  }

  return (
    <div className="flex space-x-4">
      {i18n.locales.map((locale) => (
        <Link
          key={locale}
          href={redirectedPathname(locale)}
          className={`${
            currentLang === locale ? 'font-bold' : ''
          } hover:text-blue-600`}
        >
          {locale.toUpperCase()}
        </Link>
      ))}
    </div>
  )
}