import {getRequestConfig} from 'next-intl/server';

export default getRequestConfig(async ({locale}) => {
  // 确保 locale 不是 undefined
  if (!locale) {
    locale = 'zh'; // 使用默认语言
  }
  
  try {
    return {
      locale,
      messages: (await import(`../messages/${locale}.json`)).default
    };
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);
    // 如果找不到翻译文件，使用空对象
    return {
      locale,
      messages: {}
    };
  }
});