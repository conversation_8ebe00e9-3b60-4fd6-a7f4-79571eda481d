export interface Service {
  category: string
  name: string
  description: string
}

export interface GlobalContent {
  title: string
  points: string[]
  image?: string
}

export interface Advantage {
  value: string
  title: string
  description: string
  [key: string]: string  // 添加索引签名
}

export interface Content {
  id?: number
  type: string
  title: string
  content: string
  image?: string
  lang: string
  order?: number
  active?: boolean
}