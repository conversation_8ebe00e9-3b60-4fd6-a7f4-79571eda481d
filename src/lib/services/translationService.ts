/**
 * 翻译服务
 */

// 支持的语言
export type SupportedLanguage = 'zh' | 'en' | 'ar';

// 环境变量
// 注意：我们直接在函数中使用process.env，而不是通过常量

// 语言映射
const LANGUAGE_MAP: Record<SupportedLanguage, { microsoft: string; deepl: string }> = {
  zh: { microsoft: 'zh-Hans', deepl: 'ZH' },
  en: { microsoft: 'en', deepl: 'EN' },
  ar: { microsoft: 'ar', deepl: 'AR' }
};

// 添加延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 添加翻译缓存
const translationCache: Record<string, string> = {};

// 请求计数器和时间窗口
// Microsoft Translator API
let msRequestCount = 0;
let msRequestWindow = Date.now();
const MS_MAX_REQUESTS = 5; // 每秒最大请求数
const MS_WINDOW_MS = 1000; // 时间窗口（毫秒）

// DeepL API
let deeplRequestCount = 0;
let deeplRequestWindowStart = Date.now(); // Renamed for clarity
const DEEPL_MAX_REQUESTS = 5; // 每秒最大请求数
const DEEPL_WINDOW_MS = 1000; // 时间窗口（毫秒）
// 添加重试机制
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1秒

async function retryWithDelay<T>(
  fn: () => Promise<T>,
  retries: number = MAX_RETRIES,
  delay: number = RETRY_DELAY
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (retries === 0) throw error;
    await new Promise(resolve => setTimeout(resolve, delay));
    return retryWithDelay(fn, retries - 1, delay);
  }
}

// 验证翻译结果
function validateTranslation(original: string, translated: string): boolean {
  const originalTrimmed = original.trim();
  const translatedTrimmed = translated.trim();

  // If original text was empty or whitespace, translated should also be.
  if (!originalTrimmed) {
    return !translatedTrimmed;
  }

  // If original text was not empty, translated text should not be empty.
  // This catches cases where the translation API might have failed and returned an empty string.
  if (originalTrimmed && !translatedTrimmed) {
    console.warn(`[validateTranslation] Original text was not empty, but translation is empty. Original: "${original}", Translated: "${translated}"`);
    return false;
  }
  
  // At this point, if DeepL returned the original text, or a modified text,
  // we generally trust it, especially with tag_handling='html'.
  // The previous strict check for (original === translated) is removed.
  return true;
}

/**
 * 检查文本是否包含中文
 */
function isChineseText(text: string): boolean {
  return /[\u4e00-\u9fa5]/.test(text);
}

/**
 * 处理阿拉伯文工作时间
 */
function processArabicWorkingHours(text: string): string {
  // 保持数字从左到右显示
  return text.replace(/(\d+:\d+)/g, '‎$1‎');
}

// 生成缓存键
function getCacheKey(text: string, targetLang: SupportedLanguage, sourceLang: SupportedLanguage): string {
  return `${sourceLang}:${targetLang}:${text}`;
}

/**
 * 检查DeepLX服务是否可用
 */
async function checkDeepLXAvailability(): Promise<boolean> {
  const deeplxUrl = process.env.DEEPLX_API_URL || 'http://127.0.0.1:1188/translate';

  try {
    const response = await fetch(deeplxUrl.replace('/translate', '/'), {
      method: 'GET',
      signal: AbortSignal.timeout(3000) // 3秒超时
    });
    return response.ok;
  } catch (error) {
    console.log(`[DeepLX] 服务不可用: ${error}`);
    return false;
  }
}

/**
 * 使用DeepLX本地服务翻译文本
 */
async function translateWithDeepLX(
  text: string,
  targetLang: SupportedLanguage,
  sourceLang: SupportedLanguage = 'zh'
): Promise<string> {
  console.log(`[DeepLX] 开始翻译，源语言: ${sourceLang}，目标语言: ${targetLang}`);

  const apiUrl = process.env.DEEPLX_API_URL || 'http://127.0.0.1:1188/translate';
  console.log(`[DeepLX] API URL: ${apiUrl}`);

  const deeplxParams = {
    text: text,
    source_lang: sourceLang,
    target_lang: targetLang
  };

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(deeplxParams),
      signal: AbortSignal.timeout(10000) // 10秒超时
    });

    console.log(`[DeepLX] 响应状态: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      throw new Error(`DeepLX API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`[DeepLX] 响应数据:`, JSON.stringify(data));

    if (data.code === 200) {
      console.log(`[DeepLX] 翻译成功`);
      return data.data;
    }

    throw new Error('DeepLX API返回数据格式错误');
  } catch (error) {
    console.error(`[DeepLX] 翻译失败:`, error);
    throw error;
  }
}

/**
 * 使用官方DeepL API翻译文本
 */
async function translateWithOfficialDeepL(
  text: string,
  targetLang: SupportedLanguage,
  sourceLang: SupportedLanguage = 'zh'
): Promise<string> {
  console.log(`[DeepL Official] 开始翻译，源语言: ${sourceLang}，目标语言: ${targetLang}`);

  const apiKey = process.env.DEEPL_API_KEY;
  if (!apiKey) {
    console.error('[DeepL Official] API密钥未配置');
    throw new Error('DeepL API密钥未配置');
  }

  // Implement Rate Limiting
  const currentTime = Date.now();
  if (currentTime - deeplRequestWindowStart > DEEPL_WINDOW_MS) {
    deeplRequestCount = 0;
    deeplRequestWindowStart = currentTime;
  }

  deeplRequestCount++;
  if (deeplRequestCount > DEEPL_MAX_REQUESTS) {
    const waitTime = DEEPL_WINDOW_MS - (currentTime - deeplRequestWindowStart);
    console.log(`[DeepL Official] Rate limit reached. Waiting for ${waitTime > 0 ? waitTime : DEEPL_WINDOW_MS}ms`);
    await delay(waitTime > 0 ? waitTime : DEEPL_WINDOW_MS);
    deeplRequestCount = 1;
    deeplRequestWindowStart = Date.now();
  }

  console.log(`[DeepL Official] 使用的API密钥: ${apiKey.substring(0, 10)}...`);

  // 检查是否是HTML内容
  const isHtml = text.includes('<') && text.includes('>');

  const params: any = {
    text: [text],
    target_lang: targetLang.toUpperCase(),
    source_lang: sourceLang.toUpperCase()
  };

  if (isHtml) {
    params.tag_handling = 'html';
  }

  console.log(`[DeepL Official] 请求参数:`, JSON.stringify(params));

  try {
    const apiUrl = 'https://api-free.deepl.com/v2/translate';

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params),
      signal: AbortSignal.timeout(15000) // 15秒超时
    });

    console.log(`[DeepL Official] 响应状态: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`DeepL Official API错误: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    console.log(`[DeepL Official] 响应数据:`, JSON.stringify(data));

    if (data && data.translations && data.translations[0]) {
      console.log(`[DeepL Official] 翻译成功`);
      return data.translations[0].text;
    }

    throw new Error('DeepL Official API返回数据格式错误');
  } catch (error) {
    console.error(`[DeepL Official] 翻译失败:`, error);
    throw error;
  }
}

/**
 * 智能翻译函数 - 优先使用DeepLX，失败时回退到官方API
 */
async function translateWithDeepL(
  text: string,
  targetLang: SupportedLanguage,
  sourceLang: SupportedLanguage = 'zh'
): Promise<string> {
  console.log(`[翻译] 开始智能翻译，源语言: ${sourceLang}，目标语言: ${targetLang}`);

  // 首先尝试使用DeepLX本地服务
  try {
    const isDeepLXAvailable = await checkDeepLXAvailability();
    if (isDeepLXAvailable) {
      console.log('[翻译] 使用DeepLX本地服务');
      return await translateWithDeepLX(text, targetLang, sourceLang);
    } else {
      console.log('[翻译] DeepLX服务不可用，回退到官方API');
    }
  } catch (error) {
    console.log('[翻译] DeepLX翻译失败，回退到官方API:', error);
  }

  // 回退到官方DeepL API
  try {
    console.log('[翻译] 使用官方DeepL API');
    return await translateWithOfficialDeepL(text, targetLang, sourceLang);
  } catch (error) {
    console.error('[翻译] 官方DeepL API也失败了:', error);
    return text; // 返回原文
  }
}

// 检查是否是专有名词
function isProperNoun(text: string): boolean {
  // 移除HTML标签
  const cleanText = text.replace(/<[^>]*>/g, ' ').trim();
  
  // 检查是否只包含英文字母、数字和特殊字符
  if (/^[a-zA-Z0-9\s\-_]+$/.test(cleanText)) {
    return true;
  }
  
  // 检查是否是常见的专有名词
  const commonProperNouns = [
    'epc', 'sop', 'efic', 'asiut', 'egypt',
    'fertilizer', 'petrochemical', 'chemical',
    'environmental'
  ];
  
  return commonProperNouns.some(noun => 
    cleanText.toLowerCase().includes(noun.toLowerCase())
  );
}

// 检查是否需要翻译
function needsTranslation(text: string): boolean {
  // 移除HTML标签
  const cleanText = text.replace(/<[^>]*>/g, ' ').trim();
  
  // 如果文本为空，不需要翻译
  if (!cleanText) {
    return false;
  }
  
  // 如果只包含英文字母、数字和特殊字符，不需要翻译
  if (/^[a-zA-Z0-9\s\-_]+$/.test(cleanText)) {
    return false;
  }
  
  // 如果只包含阿拉伯文字符，不需要翻译
  if (/^[\u0600-\u06FF\s]+$/.test(cleanText)) {
    return false;
  }
  
  return true;
}

/**
 * 翻译文本
 */
export async function translateText(
  text: string,
  targetLang: SupportedLanguage,
  sourceLang: SupportedLanguage = 'zh'
): Promise<string> {
  if (!text.trim()) {
    console.log('[翻译] 文本为空，返回空字符串');
    return '';
  }

  console.log(`[翻译] 开始翻译文本，源语言: ${sourceLang}，目标语言: ${targetLang}`);
  console.log(`[翻译] 原文: "${text}"`);

  try {
    // 使用DeepL API翻译
    const translated = await retryWithDelay(() => translateWithDeepL(text, targetLang, sourceLang));

    // 注意: validateTranslation 逻辑可能过于严格.
    // 如果 DeepL 返回原文 (例如专有名词、品牌名，或源/目标语言相同)，
    // 此验证可能错误地将其视为失败。可以考虑调整或暂时禁用此验证进行测试。
    // console.log(`[翻译] 原文: "${text}", 译文: "${translated}", 验证结果: ${validateTranslation(text, translated)}`);
    // 验证翻译结果
    if (validateTranslation(text, translated)) {
      return translated;
    }

    // 如果验证失败，返回原文
    console.log('[翻译] 翻译验证失败，返回原文');
    return text;
  } catch (error) {
    console.error('[翻译] 翻译失败:', error);
    return text;
  }
}

/**
 * 翻译HTML内容
 */
export async function translateHtml(
  html: string,
  targetLang: SupportedLanguage,
  sourceLang: SupportedLanguage = 'zh'
): Promise<string> {
  if (!html) return '';

  try {
    return await translateText(html, targetLang, sourceLang);
  } catch (error) {
    console.error('HTML翻译失败:', error);
    return html;
  }
}

// 批量处理字符串数组，减少API调用次数
async function batchTranslateStrings(
  texts: string[],
  targetLang: SupportedLanguage,
  sourceLang: SupportedLanguage
): Promise<string[]> {
  if (texts.length === 0) return [];

  // 过滤掉空字符串和已缓存的文本
  const textsToTranslate = texts.filter(text =>
    text && text.trim() !== '' && !translationCache[getCacheKey(text, targetLang, sourceLang)]
  );

  if (textsToTranslate.length === 0) {
    // 所有文本都已缓存，直接返回缓存结果
    return texts.map(text =>
      text && text.trim() !== ''
        ? translationCache[getCacheKey(text, targetLang, sourceLang)] || text
        : text
    );
  }

  // 对于每个文本，尝试翻译并缓存结果
  const results = await Promise.all(
    textsToTranslate.map(async text => {
      try {
        const translated = await translateText(text, targetLang, sourceLang);
        return translated;
      } catch (error) {
        console.error(`批量翻译失败:`, error);
        return text; // 失败时返回原文
      }
    })
  );

  // 创建翻译映射
  const translationMap = new Map<string, string>();
  textsToTranslate.forEach((text, index) => {
    translationMap.set(text, results[index]);
  });

  // 返回结果，保持原始顺序
  return texts.map(text =>
    text && text.trim() !== ''
      ? translationMap.get(text) || translationCache[getCacheKey(text, targetLang, sourceLang)] || text
      : text
  );
}

/**
 * 翻译对象
 */
export async function translateObject<T>(
  obj: T,
  targetLang: SupportedLanguage,
  sourceLang: SupportedLanguage = 'zh',
  depth: number = 0
): Promise<T> {
  if (depth > 5) {
    return obj;
  }

  try {
    if (typeof obj === 'string') {
      // 检查缓存
      const cacheKey = getCacheKey(obj, targetLang, sourceLang);
      if (translationCache[cacheKey]) {
        return translationCache[cacheKey] as T;
      }

      const translated = await translateText(obj, targetLang, sourceLang);
      return translated as T;
    }

    if (Array.isArray(obj)) {
      // 对于字符串数组，使用批量翻译
      if (obj.length > 0 && obj.every(item => typeof item === 'string')) {
        return batchTranslateStrings(obj as string[], targetLang, sourceLang) as T;
      }

      // 对于其他类型的数组，逐个处理
      return Promise.all(obj.map(item => translateObject(item, targetLang, sourceLang, depth + 1))) as Promise<T>;
    }

    if (typeof obj === 'object' && obj !== null) {
      const translatedObj: Record<string, any> = {};

      // 收集需要翻译的字符串字段
      const stringFields: { key: string; value: string }[] = [];

      // 首先处理所有字段，收集字符串字段
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          // 跳过不需要翻译的字段
          if (['id', 'createdAt', 'updatedAt', 'mapType', 'lat', 'lng', 'wechatContact', 'whatsappContact', 'telegramContact', 'messengerContact'].includes(key)) {
            translatedObj[key] = obj[key];
            continue;
          }

          // 收集字符串字段
          if (typeof obj[key] === 'string' && obj[key] && key !== 'workingHours') {
            stringFields.push({ key, value: obj[key] as string });
          } else if (key === 'workingHours' && typeof obj[key] === 'string' && targetLang === 'ar') {
            // 特殊处理工作时间字段
            const translated = await translateText(obj[key] as string, targetLang, sourceLang);
            translatedObj[key] = processArabicWorkingHours(translated);
          } else {
            // 处理非字符串字段
            translatedObj[key] = await translateObject(obj[key], targetLang, sourceLang, depth + 1);
          }
        }
      }

      // 批量翻译字符串字段
      if (stringFields.length > 0) {
        const textsToTranslate = stringFields.map(field => field.value);
        const translatedTexts = await batchTranslateStrings(textsToTranslate, targetLang, sourceLang);

        // 将翻译结果应用到对象
        stringFields.forEach((field, index) => {
          translatedObj[field.key] = translatedTexts[index];
        });
      }

      return translatedObj as T;
    }

    return obj;
  } catch (error) {
    console.error('对象翻译失败:', error);
    // 发生错误时返回原始对象
    return obj;
  }
}
