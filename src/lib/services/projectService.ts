// 项目数据服务 - 集中管理项目数据的访问

// 定义项目类型接口
export interface Project {
  id: number;
  title: string;
  category: string;
  description: string;
  location: string;
  year: string;
  imageUrl: string;
  status: 'published' | 'draft';
}

// 模拟数据库中的项目数据
export const projectsData: Record<string, Project[]> = {
  zh: [
    {
      id: 1,
      title: '某石化企业乙烯装置改造项目',
      category: 'petrochemical',
      description: '为客户提供乙烯装置的技术改造服务，提高生产效率和安全性。',
      location: '上海',
      year: '2022',
      imageUrl: '/images/projects/project1.jpg',
      status: 'published'
    },
    {
      id: 2,
      title: '某化工厂废水处理系统设计',
      category: 'chemical',
      description: '设计并实施化工厂废水处理系统，确保排放达标。',
      location: '江苏',
      year: '2021',
      imageUrl: '/images/projects/project2.jpg',
      status: 'published'
    },
    // ... 其他项目数据保持不变，只添加status字段
  ],
  en: [
    // ... 英文项目数据保持不变，只添加status字段
  ],
  ar: [
    // ... 阿拉伯语项目数据保持不变，只添加status字段
  ]
};

// 获取项目列表（可根据语言和状态过滤）
export async function getProjects(locale: string = 'zh', status?: string): Promise<Project[]> {
  // 模拟数据库查询延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  let projects = projectsData[locale as keyof typeof projectsData] || projectsData.zh;
  
  // 如果指定了状态，进行过滤
  if (status) {
    projects = projects.filter(project => project.status === status);
  }
  
  return projects;
}

// 获取单个项目
export async function getProjectById(id: number, locale: string = 'zh'): Promise<Project | undefined> {
  const projects = projectsData[locale as keyof typeof projectsData] || projectsData.zh;
  return projects.find(project => project.id === id);
}

// 更新项目（仅管理员API使用）
export async function updateProject(id: number, data: Partial<Project>, locale: string = 'zh'): Promise<Project | null> {
  const projects = projectsData[locale as keyof typeof projectsData] || projectsData.zh;
  const index = projects.findIndex(project => project.id === id);
  
  if (index !== -1) {
    // 确保更新后的项目包含所有必要字段
    projects[index] = { 
      ...projects[index], 
      ...data,
      // 确保ID不变
      id: projects[index].id
    };
    
    // 同步更新其他语言版本的基本信息（如果存在）
    if (data.imageUrl || data.category || data.year) {
      for (const lang in projectsData) {
        if (lang !== locale) {
          const otherLangProjects = projectsData[lang];
          const otherIndex = otherLangProjects.findIndex(p => p.id === id);
          if (otherIndex !== -1) {
            // 只同步通用字段
            if (data.imageUrl) otherLangProjects[otherIndex].imageUrl = data.imageUrl;
            if (data.category) otherLangProjects[otherIndex].category = data.category;
            if (data.year) otherLangProjects[otherIndex].year = data.year;
            if (data.status) otherLangProjects[otherIndex].status = data.status;
          }
        }
      }
    }
    
    return projects[index];
  }
  
  return null;
}

// 创建项目（仅管理员API使用）
export async function createProject(data: Omit<Project, 'id'>, locale: string = 'zh'): Promise<Project> {
  const projects = projectsData[locale as keyof typeof projectsData] || projectsData.zh;
  
  // 找出所有语言中的最大ID
  let maxId = 0;
  for (const lang in projectsData) {
    const langMaxId = Math.max(...projectsData[lang].map(p => p.id), 0);
    maxId = Math.max(maxId, langMaxId);
  }
  
  const newId = maxId + 1;
  
  // 确保新项目包含所有必要字段
  const newProject: Project = {
    id: newId,
    title: data.title || '',
    category: data.category || '',
    description: data.description || '',
    location: data.location || '',
    year: data.year || new Date().getFullYear().toString(),
    imageUrl: data.imageUrl || '/images/projects/default.jpg',
    status: data.status || 'draft'
  };
  
  projects.push(newProject);
  
  // 如果是发布状态，同步创建其他语言版本的占位项目
  if (newProject.status === 'published') {
    for (const lang in projectsData) {
      if (lang !== locale) {
        const placeholderTitle = lang === 'en' ? 
          `New Project (needs translation)` : 
          lang === 'ar' ? 
          `مشروع جديد (يحتاج إلى ترجمة)` : 
          '新项目（需要翻译）';
        
        projectsData[lang].push({
          ...newProject,
          title: placeholderTitle,
          description: placeholderTitle,
          status: 'draft' // 其他语言版本默认为草稿状态
        });
      }
    }
  }
  
  return newProject;
}

// 删除项目（仅管理员API使用）
export async function deleteProject(id: number, locale: string = 'zh'): Promise<Project | null> {
  // 从所有语言版本中删除该项目
  let deletedProject: Project | null = null;
  
  for (const lang in projectsData) {
    const projects = projectsData[lang];
    const index = projects.findIndex(project => project.id === id);
    
    if (index !== -1) {
      const deleted = projects.splice(index, 1)[0];
      if (lang === locale) {
        deletedProject = deleted;
      }
    }
  }
  
  return deletedProject;
}