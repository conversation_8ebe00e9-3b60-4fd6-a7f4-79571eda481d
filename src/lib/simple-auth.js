// 简单的认证中间件
export function isAdmin(request) {
  // 检查cookie或session
  const cookies = request.headers.get('cookie') || '';
  return cookies.includes('admin_logged_in=true');
}

export function createAuthResponse() {
  const response = new Response(JSON.stringify({ success: true }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
  
  response.headers.set('Set-Cookie', 'admin_logged_in=true; Path=/; HttpOnly; Max-Age=604800');
  return response;
}

export const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};
