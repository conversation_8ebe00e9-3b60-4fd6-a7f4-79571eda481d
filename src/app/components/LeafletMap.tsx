'use client';

import { useEffect, useRef } from 'react';

interface LeafletMapProps {
  lat: number;
  lng: number;
  zoom?: number;
}

export default function LeafletMap({ lat, lng, zoom = 13 }: LeafletMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markerRef = useRef<any>(null);

  useEffect(() => {
    // 动态导入 Leaflet，确保只在客户端执行
    const initMap = async () => {
      if (typeof window === 'undefined' || !mapRef.current) return;
      
      try {
        // 动态导入 Leaflet
        const L = (await import('leaflet')).default;
        
        // 导入 CSS
        // 在 Next.js 中需要使用 require 语法来导入 CSS
        require('leaflet/dist/leaflet.css');
        
        // 修复图标问题
        delete (L.Icon.Default.prototype as any)._getIconUrl;
        L.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
          iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
          shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
        });
        
        // 清理之前的地图实例
        if (mapInstanceRef.current) {
          mapInstanceRef.current.remove();
        }
        
        // 创建新的地图实例
        mapInstanceRef.current = L.map(mapRef.current).setView([lat, lng], zoom);
        
        // 添加图层
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(mapInstanceRef.current);
        
        // 添加标记
        markerRef.current = L.marker([lat, lng]).addTo(mapInstanceRef.current);
      } catch (error) {
        console.error('初始化地图时出错:', error);
      }
    };
    
    initMap();
    
    // 组件卸载时清理
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [lat, lng, zoom]);
  
  return <div ref={mapRef} className="h-64 w-full rounded-lg overflow-hidden" />;
}