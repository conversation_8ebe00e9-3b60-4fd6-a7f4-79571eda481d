'use client';

import Link from 'next/link';
import { useState } from 'react';
import { usePathname } from 'next/navigation'; // 添加这一行

interface NavbarProps {
  locale: string;
  t: any;
  // 添加 currentPath 属性
  currentPath?: string; // 使用可选属性，这样即使不传也不会报错
}

export default function Navbar({ locale, t }: NavbarProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname(); // 使用usePathname代替location.pathname

  return (
    <nav className="bg-white shadow-md">
      {/* 增加容器的内边距和高度 */}
      <div className="container mx-auto px-6 py-4">
        <div className="flex justify-between items-center">
          {/* Logo 位置 */}
          <div className="flex items-center">
            {/* 这里预留放置logo的位置 */}
            <div className="w-40 h-10 flex items-center">
              <span className="text-xl font-bold text-gray-800">MOCO</span>
            </div>
        </div>
        
        {/* 桌面导航 */}
        <div className="hidden md:flex space-x-8">
          <Link href={`/${locale}`} className="text-gray-700 hover:text-blue-800">
            {t.nav.home}
          </Link>
          <Link href={`/${locale}/about`} className="text-gray-700 hover:text-blue-800">
            {t.nav.about}
          </Link>
          <Link href={`/${locale}/services`} className="text-gray-700 hover:text-blue-800">
            {t.nav.services}
          </Link>
          <Link href={`/${locale}/projects`} className="text-gray-700 hover:text-blue-800">
            {t.nav.projects}
          </Link>
          <Link href={`/${locale}/contact`} className="text-gray-700 hover:text-blue-800">
            {t.nav.contact}
          </Link>
        </div>
        
        {/* 语言切换 */}
        <div className="hidden md:flex">
          <Link href={`/zh${locale !== '/' ? pathname.substring(3) : ''}`} 
            className={`px-2 ${locale === 'zh' ? 'font-bold text-blue-800' : 'text-gray-600'}`}>
            中文
          </Link>
          <Link href={`/en${locale !== '/' ? pathname.substring(3) : ''}`} 
            className={`px-2 ${locale === 'en' ? 'font-bold text-blue-800' : 'text-gray-600'}`}>
            English
          </Link>
          <Link href={`/ar${locale !== '/' ? location.pathname.substring(3) : ''}`} className={`px-2 ${locale === 'ar' ? 'font-bold text-blue-800' : 'text-gray-600'}`}>
            العربية
          </Link>
        </div>
        
        {/* 移动端菜单按钮 */}
        <div className="md:hidden">
          <button 
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="text-gray-700 hover:text-blue-800 focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {mobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>
      </div>
      
      {/* 移动端导航菜单 */}
      {mobileMenuOpen && (
        <div className="md:hidden mt-4 px-4 py-2 bg-white border-t border-gray-200">
          <div className="flex flex-col space-y-3">
            <Link href={`/${locale}`} className="text-gray-700 hover:text-blue-800 py-2">
              {t.nav.home}
            </Link>
            <Link href={`/${locale}/about`} className="text-gray-700 hover:text-blue-800 py-2">
              {t.nav.about}
            </Link>
            <Link href={`/${locale}/services`} className="text-gray-700 hover:text-blue-800 py-2">
              {t.nav.services}
            </Link>
            <Link href={`/${locale}/projects`} className="text-gray-700 hover:text-blue-800 py-2">
              {t.nav.projects}
            </Link>
            <Link href={`/${locale}/contact`} className="text-gray-700 hover:text-blue-800 py-2">
              {t.nav.contact}
            </Link>
            
            {/* 移动端语言切换 */}
            <div className="flex space-x-4 pt-2 border-t border-gray-200">
              <Link href={`/zh${locale !== '/' ? location.pathname.substring(3) : ''}`} className={`px-2 ${locale === 'zh' ? 'font-bold text-blue-800' : 'text-gray-600'}`}>
                中文
              </Link>
              <Link href={`/en${locale !== '/' ? location.pathname.substring(3) : ''}`} className={`px-2 ${locale === 'en' ? 'font-bold text-blue-800' : 'text-gray-600'}`}>
                English
              </Link>
              <Link href={`/ar${locale !== '/' ? location.pathname.substring(3) : ''}`} className={`px-2 ${locale === 'ar' ? 'font-bold text-blue-800' : 'text-gray-600'}`}>
                العربية
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
    </nav>
  );
}