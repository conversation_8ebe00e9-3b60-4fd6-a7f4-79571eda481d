'use client';

import Navbar from './Navbar';
import Footer from './Footer';

interface PageLayoutProps {
  children: React.ReactNode;
  locale: string;
  t: any;
  currentPath: string;
}

export default function PageLayout({ children, locale, t, currentPath }: PageLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 使用共享导航栏组件 */}
      <Navbar 
        locale={locale} 
        t={t}
        currentPath={currentPath}
      />
      
      {/* 添加统一的间隔元素 */}
      <div className="h-12"></div>
      
      {/* 页面主要内容 */}
      <div className="container mx-auto px-4 py-8 bg-gray-50">
        {children}
      </div>
      
      {/* 使用共享页脚组件 */}
      <Footer locale={locale} t={t} />
    </div>
  );
}