@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #1e40af;
  --primary-dark: #1e3a8a;
  --secondary: #f3f4f6;
  --text-primary: #171717;
  --text-secondary: #4b5563;
  --background: #ffffff;
  --foreground: #171717;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* 通用组件样式 */
.btn-primary {
  @apply bg-blue-800 text-white px-6 py-3 rounded-md font-semibold hover:bg-blue-900 transition;
}

.section-title {
  @apply text-3xl font-bold mb-8 text-center;
}

.card {
  @apply bg-white p-6 rounded-lg shadow-md;
}

/* 语言选择器样式 */
.flex > a {
  margin: 0 0.5rem;
}

/* RTL 支持 - 确保语言选择器在阿拉伯语界面中有正确的间距 */
body.rtl .flex > a {
  margin: 0 0.5rem;
}

/* RTL 支持 - 基础布局调整 */
body.rtl {
  direction: rtl;
  text-align: right;
}

/* 响应式导航 */
.mobile-nav {
  @apply fixed top-0 left-0 w-full h-full bg-white z-50 transform transition-transform duration-300;
}

.mobile-nav.open {
  @apply translate-x-0;
}

.mobile-nav.closed {
  @apply -translate-x-full;
}

body.rtl .mobile-nav.open {
  @apply translate-x-0;
}

body.rtl .mobile-nav.closed {
  @apply translate-x-full;
}