import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

const dataFilePath = path.join(process.cwd(), 'data', 'about.json');

// 确保数据目录存在
async function ensureDataDir() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch (error) {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// 获取关于我们数据
export async function GET() {
  try {
    await ensureDataDir();
    
    try {
      const data = await fs.readFile(dataFilePath, 'utf8');
      return NextResponse.json(JSON.parse(data));
    } catch (error) {
      // 如果文件不存在，返回默认数据
      const defaultData = {
        companyName: 'MOCO Engineering',
        foundedYear: '2010',
        mission: '为客户提供高质量的工程解决方案',
        vision: '成为全球领先的工程服务提供商',
        description: '我们是一家专业的化工与石化工程设计、采购、施工(EPC)解决方案提供商。',
        history: '',
        values: ['专业', '创新', '诚信'],
        achievements: ['成功完成100+项目']
      };
      
      // 保存默认数据到文件
      await fs.writeFile(dataFilePath, JSON.stringify(defaultData, null, 2));
      return NextResponse.json(defaultData);
    }
  } catch (error) {
    return NextResponse.json(
      { error: '获取关于我们数据失败' },
      { status: 500 }
    );
  }
}

// 更新关于我们数据
export async function PUT(request: Request) {
  try {
    await ensureDataDir();
    
    const data = await request.json();
    await fs.writeFile(dataFilePath, JSON.stringify(data, null, 2));
    
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: '更新关于我们数据失败' },
      { status: 500 }
    );
  }
}