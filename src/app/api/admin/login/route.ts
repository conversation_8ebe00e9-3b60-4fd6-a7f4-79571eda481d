import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const body = await request.json();
    const { username, password } = body;
    
    console.log('登录尝试:', username);
    
    // 简单的硬编码验证 - 支持多个密码
    if (username === 'admin' && (password === 'admin123' || password === 'password' || password === 'moco0520')) {
      // 创建响应并设置cookie
      const response = NextResponse.json({ success: true });
      response.cookies.set('admin_logged_in', 'true', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 60 * 60 * 24 * 7, // 一周
        path: '/',
        sameSite: 'lax',
      });
      
      console.log('登录成功，设置了 cookie');
      return response;
    }
    
    console.log('登录失败：用户名或密码错误');
    
    return NextResponse.json(
      { success: false, error: '用户名或密码错误' },
      { status: 401 }
    );
  } catch (error) {
    console.error('登录处理出错:', error);
    return NextResponse.json({ error: '登录失败' }, { status: 500 });
  }
}
