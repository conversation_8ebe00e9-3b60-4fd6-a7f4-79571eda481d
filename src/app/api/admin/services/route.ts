import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

// 服务数据文件路径
const dataFilePath = path.join(process.cwd(), 'data', 'services.json');

// 确保数据目录存在
async function ensureDataDir() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch (error) {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// 读取服务数据
async function readServicesData() {
  try {
    await ensureDataDir();
    const data = await fs.readFile(dataFilePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // 如果文件不存在或解析错误，返回空数组
    return { services: [] };
  }
}

// 写入服务数据
async function writeServicesData(data: any) {
  await ensureDataDir();
  await fs.writeFile(dataFilePath, JSON.stringify(data, null, 2), 'utf-8');
}

// GET 获取所有服务
export async function GET() {
  try {
    const data = await readServicesData();
    return NextResponse.json(data);
  } catch (error) {
    console.error('获取服务列表失败:', error);
    return NextResponse.json(
      { error: '获取服务列表失败' },
      { status: 500 }
    );
  }
}

// POST 创建新服务
export async function POST(request: Request) {
  try {
    const newService = await request.json();
    
    // 验证必填字段
    if (!newService.id || !newService.title || !newService.description) {
      return NextResponse.json(
        { error: '缺少必填字段' },
        { status: 400 }
      );
    }
    
    // 读取现有数据
    const data = await readServicesData();
    
    // 检查ID是否已存在
    if (data.services.some((service: any) => service.id === newService.id)) {
      return NextResponse.json(
        { error: '服务ID已存在' },
        { status: 400 }
      );
    }
    
    // 添加新服务
    data.services.push(newService);
    
    // 保存数据
    await writeServicesData(data);
    
    return NextResponse.json({ success: true, service: newService }, { status: 201 });
  } catch (error) {
    console.error('创建服务失败:', error);
    return NextResponse.json(
      { error: '创建服务失败' },
      { status: 500 }
    );
  }
}