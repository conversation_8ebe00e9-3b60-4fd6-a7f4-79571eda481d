import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

// 服务数据文件路径
const dataFilePath = path.join(process.cwd(), 'data', 'services.json');

// 读取服务数据
async function readServicesData() {
  try {
    const data = await fs.readFile(dataFilePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // 如果文件不存在或解析错误，返回空数组
    return { services: [] };
  }
}

// 写入服务数据
async function writeServicesData(data: any) {
  await fs.writeFile(dataFilePath, JSON.stringify(data, null, 2), 'utf-8');
}

// GET 获取单个服务
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const data = await readServicesData();
    
    // 查找指定ID的服务
    const service = data.services.find((s: any) => s.id === id);
    
    if (!service) {
      return NextResponse.json(
        { error: '服务不存在' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ service });
  } catch (error) {
    console.error('获取服务详情失败:', error);
    return NextResponse.json(
      { error: '获取服务详情失败' },
      { status: 500 }
    );
  }
}

// PUT 更新服务
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const updatedService = await request.json();
    
    // 验证必填字段
    if (!updatedService.title || !updatedService.description) {
      return NextResponse.json(
        { error: '缺少必填字段' },
        { status: 400 }
      );
    }
    
    // 读取现有数据
    const data = await readServicesData();
    
    // 查找服务索引
    const serviceIndex = data.services.findIndex((s: any) => s.id === id);
    
    if (serviceIndex === -1) {
      return NextResponse.json(
        { error: '服务不存在' },
        { status: 404 }
      );
    }
    
    // 更新服务（保持ID不变）
    updatedService.id = id;
    data.services[serviceIndex] = updatedService;
    
    // 保存数据
    await writeServicesData(data);
    
    return NextResponse.json({ success: true, service: updatedService });
  } catch (error) {
    console.error('更新服务失败:', error);
    return NextResponse.json(
      { error: '更新服务失败' },
      { status: 500 }
    );
  }
}

// DELETE 删除服务
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // 读取现有数据
    const data = await readServicesData();
    
    // 查找服务索引
    const serviceIndex = data.services.findIndex((s: any) => s.id === id);
    
    if (serviceIndex === -1) {
      return NextResponse.json(
        { error: '服务不存在' },
        { status: 404 }
      );
    }
    
    // 删除服务
    data.services.splice(serviceIndex, 1);
    
    // 保存数据
    await writeServicesData(data);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('删除服务失败:', error);
    return NextResponse.json(
      { error: '删除服务失败' },
      { status: 500 }
    );
  }
}