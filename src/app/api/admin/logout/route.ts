import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    // 创建响应并在响应中设置cookie以清除它
    const response = NextResponse.json({ success: true });
    response.cookies.set('admin_logged_in', '', { maxAge: 0 });
    
    return response;
  } catch (error) {
    console.error('登出处理出错:', error);
    return NextResponse.json({ error: '登出失败' }, { status: 500 });
  }
}