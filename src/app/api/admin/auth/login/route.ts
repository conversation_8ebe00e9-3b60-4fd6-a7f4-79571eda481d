import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  const body = await request.json();
  const { username, password } = body;
  
  // 简单的硬编码验证
  if (username === 'admin' && password === 'password') {
    // 创建响应并在响应中设置cookie
    const response = NextResponse.json({ success: true });
    response.cookies.set('admin_auth', 'true', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60 * 24 * 7, // 一周
      path: '/',
    });
    
    return response;
  }
  
  return NextResponse.json(
    { success: false, error: '用户名或密码错误' },
    { status: 401 }
  );
}