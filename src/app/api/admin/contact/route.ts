import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

const dataFilePath = path.join(process.cwd(), 'data', 'contact.json');

// 确保数据目录存在
async function ensureDataDir() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch (error) {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// 获取联系我们数据
export async function GET() {
  try {
    await ensureDataDir();
    
    try {
      const data = await fs.readFile(dataFilePath, 'utf8');
      return NextResponse.json(JSON.parse(data));
    } catch (error) {
      // 如果文件不存在，返回默认数据
      const defaultData = {
        address: '上海市浦东新区',
        phone: '+86 21 1234 5678',
        email: '<EMAIL>',
        workingHours: '周一至周五 9:00-18:00',
        mapLocation: {
          lat: '31.2304',
          lng: '121.4737',
          mapType: 'baidu' // 添加默认地图类型
        },
        socialMedia: {
          weibo: '',
          wechat: 'MOCO_Engineering',
          linkedin: ''
        }
      };
      
      // 保存默认数据到文件
      await fs.writeFile(dataFilePath, JSON.stringify(defaultData, null, 2));
      return NextResponse.json(defaultData);
    }
  } catch (error) {
    return NextResponse.json(
      { error: '获取联系我们数据失败' },
      { status: 500 }
    );
  }
}

// 更新联系我们数据
export async function PUT(request: Request) {
  try {
    await ensureDataDir();
    
    const data = await request.json();
    await fs.writeFile(dataFilePath, JSON.stringify(data, null, 2));
    
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: '更新联系我们数据失败' },
      { status: 500 }
    );
  }
}

// 修改 ContactData 接口，增加地图类型
interface ContactData {
  address: string;
  phone: string;
  email: string;
  workingHours: string;
  mapLocation: {
    lat: string;
    lng: string;
    mapType: 'baidu' | 'apple' | 'google' | null; // 新增地图类型字段
  };
  socialMedia: {
    weibo: string;
    wechat: string;
    linkedin: string;
  };
}