import { NextResponse } from 'next/server';
import { getProjects, createProject, updateProject, deleteProject, getProjectById } from '@/lib/services/projectService';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/admin/auth';

// 验证管理员权限
async function isAdmin() {
  try {
    const session = await getServerSession(authOptions);
    // 由于auth.ts中返回的用户没有role字段，我们根据用户名判断是否为管理员
    return session?.user?.name === 'Admin';
  } catch (error) {
    console.error('权限验证错误:', error);
    return false;
  }
}

// 错误响应处理函数
function errorResponse(message: string, status: number = 400) {
  return NextResponse.json({ error: message }, { status });
}

export async function GET(request: Request) {
  try {
    // 验证管理员权限
    if (!await isAdmin()) {
      return errorResponse('未授权访问', 401);
    }
    
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || 'zh';
    const id = searchParams.get('id');
    
    if (id) {
      // 验证ID格式
      const projectId = parseInt(id);
      if (isNaN(projectId)) {
        return errorResponse('无效的项目ID');
      }
      
      // 获取单个项目
      const project = await getProjectById(projectId, locale);
      if (!project) {
        return errorResponse('项目不存在', 404);
      }
      return NextResponse.json(project);
    }
    
    // 获取所有项目（包括草稿）
    const projects = await getProjects(locale);
    
    return NextResponse.json(projects);
  } catch (error) {
    console.error('获取项目数据错误:', error);
    return errorResponse('服务器内部错误', 500);
  }
}

// 在POST方法中添加详细的日志
export async function POST(request: Request) {
  try {
    console.log('接收到创建项目请求');
    const data = await request.json();
    console.log('项目数据:', data);
    
    // 这里是您的项目创建逻辑
    // ...
    
    // 确保返回成功响应
    return NextResponse.json({ success: true, message: '项目创建成功' });
  } catch (error) {
    console.error('创建项目失败:', error);
    return NextResponse.json({ 
      error: '创建项目失败', 
      details: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    // 验证管理员权限
    if (!await isAdmin()) {
      return errorResponse('未授权访问', 401);
    }
    
    const data = await request.json();
    const { id, locale = 'zh', ...updateData } = data;
    
    // 验证ID
    if (!id) {
      return errorResponse('缺少项目ID');
    }
    
    const projectId = parseInt(id);
    if (isNaN(projectId)) {
      return errorResponse('无效的项目ID');
    }
    
    // 检查项目是否存在
    const existingProject = await getProjectById(projectId, locale);
    if (!existingProject) {
      return errorResponse('项目不存在', 404);
    }
    
    const updatedProject = await updateProject(projectId, updateData, locale);
    
    return NextResponse.json(updatedProject);
  } catch (error) {
    console.error('更新项目错误:', error);
    return errorResponse('服务器内部错误', 500);
  }
}

export async function DELETE(request: Request) {
  try {
    // 验证管理员权限
    if (!await isAdmin()) {
      return errorResponse('未授权访问', 401);
    }
    
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const locale = searchParams.get('locale') || 'zh';
    
    // 验证ID
    if (!id) {
      return errorResponse('缺少项目ID');
    }
    
    const projectId = parseInt(id);
    if (isNaN(projectId)) {
      return errorResponse('无效的项目ID');
    }
    
    // 检查项目是否存在
    const existingProject = await getProjectById(projectId, locale);
    if (!existingProject) {
      return errorResponse('项目不存在', 404);
    }
    
    const deletedProject = await deleteProject(projectId, locale);
    
    return NextResponse.json({ 
      success: true, 
      message: '项目已成功删除',
      deletedProject 
    });
  } catch (error) {
    console.error('删除项目错误:', error);
    return errorResponse('服务器内部错误', 500);
  }
}