import { NextResponse } from 'next/server';
import { getProjectById, updateProject, deleteProject } from '@/lib/services/projectService';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/admin/auth';

// 验证管理员权限
async function isAdmin() {
  try {
    const session = await getServerSession(authOptions);
    return session?.user?.name === 'Admin';
  } catch (error) {
    console.error('权限验证错误:', error);
    return false;
  }
}

// 错误响应处理函数
function errorResponse(message: string, status: number = 400) {
  return NextResponse.json({ error: message }, { status });
}

// GET 获取单个项目
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    if (!await isAdmin()) {
      return errorResponse('未授权访问', 401);
    }

    const { id } = params;
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || 'zh';
    
    // 验证ID格式
    const projectId = parseInt(id);
    if (isNaN(projectId)) {
      return errorResponse('无效的项目ID');
    }
    
    // 获取项目
    const project = await getProjectById(projectId, locale);
    
    if (!project) {
      return errorResponse('项目不存在', 404);
    }
    
    return NextResponse.json(project);
  } catch (error) {
    console.error('获取项目详情失败:', error);
    return errorResponse('服务器内部错误', 500);
  }
}

// PUT 更新项目
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    if (!await isAdmin()) {
      return errorResponse('未授权访问', 401);
    }

    const { id } = params;
    const data = await request.json();
    const { locale = 'zh', ...updateData } = data;
    
    // 验证ID格式
    const projectId = parseInt(id);
    if (isNaN(projectId)) {
      return errorResponse('无效的项目ID');
    }
    
    // 验证必填字段
    if (!updateData.title || !updateData.category || !updateData.description) {
      return errorResponse('缺少必填字段: title, category, description');
    }
    
    // 检查项目是否存在
    const existingProject = await getProjectById(projectId, locale);
    if (!existingProject) {
      return errorResponse('项目不存在', 404);
    }
    
    // 更新项目
    const updatedProject = await updateProject(projectId, updateData, locale);
    
    if (!updatedProject) {
      return errorResponse('更新项目失败', 500);
    }
    
    return NextResponse.json(updatedProject);
  } catch (error) {
    console.error('更新项目失败:', error);
    return errorResponse('服务器内部错误', 500);
  }
}

// DELETE 删除项目
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // 验证管理员权限
    if (!await isAdmin()) {
      return errorResponse('未授权访问', 401);
    }

    const { id } = params;
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || 'zh';
    
    // 验证ID格式
    const projectId = parseInt(id);
    if (isNaN(projectId)) {
      return errorResponse('无效的项目ID');
    }
    
    // 检查项目是否存在
    const existingProject = await getProjectById(projectId, locale);
    if (!existingProject) {
      return errorResponse('项目不存在', 404);
    }
    
    // 删除项目
    const deletedProject = await deleteProject(projectId, locale);
    
    if (!deletedProject) {
      return errorResponse('删除项目失败', 500);
    }
    
    return NextResponse.json({ 
      success: true, 
      message: '项目已成功删除',
      deletedProject 
    });
  } catch (error) {
    console.error('删除项目失败:', error);
    return errorResponse('服务器内部错误', 500);
  }
}