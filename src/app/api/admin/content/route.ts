import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

// 内容数据文件路径
const dataFilePath = path.join(process.cwd(), 'data', 'content.json');

// 确保数据目录存在
async function ensureDataDir() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch (error) {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// 读取内容数据
async function readContentData() {
  try {
    await ensureDataDir();
    const data = await fs.readFile(dataFilePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // 如果文件不存在或解析错误，返回默认内容
    return {
      home: {
        zh: {
          hero: {
            title: "创新数字解决方案",
            subtitle: "我们帮助企业通过技术实现转型和增长",
            cta: "了解更多"
          },
          about: {
            title: "关于我们",
            content: "MOCO是一家专注于提供创新数字解决方案的公司。我们的团队由经验丰富的专业人士组成，致力于帮助企业实现数字化转型。"
          }
        },
        en: {
          hero: {
            title: "Innovative Digital Solutions",
            subtitle: "We help businesses transform and grow through technology",
            cta: "Learn More"
          },
          about: {
            title: "About Us",
            content: "MOCO is a company focused on providing innovative digital solutions. Our team consists of experienced professionals dedicated to helping businesses achieve digital transformation."
          }
        }
      },
      contact: {
        zh: {
          title: "联系我们",
          address: "中国上海市浦东新区张江高科技园区",
          email: "<EMAIL>",
          phone: "+86 21 5555 5555"
        },
        en: {
          title: "Contact Us",
          address: "Zhangjiang Hi-Tech Park, Pudong New Area, Shanghai, China",
          email: "<EMAIL>",
          phone: "+86 21 5555 5555"
        }
      }
    };
  }
}

// 写入内容数据
async function writeContentData(data: any) {
  await ensureDataDir();
  await fs.writeFile(dataFilePath, JSON.stringify(data, null, 2), 'utf-8');
}

// GET 获取所有内容
export async function GET() {
  try {
    const data = await readContentData();
    return NextResponse.json(data);
  } catch (error) {
    console.error('获取内容失败:', error);
    return NextResponse.json(
      { error: '获取内容失败' },
      { status: 500 }
    );
  }
}

// PUT 更新内容
export async function PUT(request: Request) {
  try {
    const updatedContent = await request.json();
    
    // 保存数据
    await writeContentData(updatedContent);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('更新内容失败:', error);
    return NextResponse.json(
      { error: '更新内容失败' },
      { status: 500 }
    );
  }
}