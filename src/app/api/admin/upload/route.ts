import { NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/admin/auth';

// 验证管理员权限
async function isAdmin() {
  try {
    const session = await getServerSession(authOptions);
    return session?.user?.name === 'Admin';
  } catch (error) {
    console.error('权限验证错误:', error);
    return false;
  }
}

export async function POST(request: Request) {
  try {

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: '未找到文件' }, { status: 400 });
    }

    console.log('接收到文件:', file.name, '类型:', file.type, '大小:', file.size);

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: '只允许上传图片文件' }, { status: 400 });
    }

    // 读取文件内容
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // 创建上传目录
    const uploadDir = join(process.cwd(), 'public', 'images', 'projects');
    console.log('上传目录:', uploadDir);
    
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (err) {
      console.error('创建目录失败:', err);
      return NextResponse.json({ error: '创建上传目录失败' }, { status: 500 });
    }

    // 生成唯一文件名
    const fileName = `${Date.now()}-${file.name.replace(/\s+/g, '-')}`;
    const filePath = join(uploadDir, fileName);
    console.log('文件将保存到:', filePath);

    // 写入文件
    try {
      await writeFile(filePath, buffer);
      console.log('文件保存成功');
    } catch (err) {
      console.error('写入文件失败:', err);
      return NextResponse.json({ error: '保存文件失败' }, { status: 500 });
    }

    // 返回文件URL
    const fileUrl = `/images/projects/${fileName}`;
    console.log('文件URL:', fileUrl);

    return NextResponse.json({ 
      success: true, 
      url: fileUrl 
    });
  } catch (error) {
    console.error('上传文件错误:', error);
    return NextResponse.json({ 
      error: '上传文件失败', 
      details: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
}