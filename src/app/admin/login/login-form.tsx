'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function LoginForm() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  // 清除之前的登录状态
  useEffect(() => {
    localStorage.removeItem('admin_logged_in');
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 防止重复提交
    if (loading) return;
    
    setLoading(true);
    setError('');
    
    console.log('提交登录表单:', username, password);

    // 简单的客户端验证
    if (username === 'admin' && password === 'password') {
      try {
        // 直接在客户端设置登录状态
        localStorage.setItem('admin_logged_in', 'true');
        console.log('登录成功，设置了本地存储');
        
        // 使用 Next.js 路由器进行导航
        router.push('/admin/dashboard');
      } catch (error) {
        console.error('登录时出错:', error);
        setError('登录失败，请稍后重试');
        setLoading(false);
      }
    } else {
      setError('用户名或密码错误');
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <div className="mb-4">
        <label className="block text-gray-700 mb-2">用户名</label>
        <input 
          type="text" 
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          className="w-full p-2 border rounded" 
          required 
          autoComplete="off"
        />
      </div>
      
      <div className="mb-6">
        <label className="block text-gray-700 mb-2">密码</label>
        <input 
          type="password" 
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full p-2 border rounded" 
          required 
          autoComplete="off"
        />
      </div>
      
      <button 
        type="submit"
        disabled={loading}
        className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loading ? '登录中...' : '登录'}
      </button>
    </form>
  );
}