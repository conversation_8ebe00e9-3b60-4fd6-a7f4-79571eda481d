'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AboutPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [aboutData, setAboutData] = useState({
    companyName: '',
    foundedYear: '',
    mission: '',
    vision: '',
    description: '',
    history: '',
    values: [''],
    achievements: ['']
  });

  // 获取关于我们的数据
  useEffect(() => {
    const fetchAboutData = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/admin/about');
        if (!response.ok) {
          throw new Error('获取关于我们数据失败');
        }
        const data = await response.json();
        setAboutData(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAboutData();
  }, []);

  // 处理表单字段变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setAboutData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // 处理数组字段变化
  const handleArrayChange = (field: 'values' | 'achievements', index: number, value: string) => {
    const newArray = [...aboutData[field]];
    newArray[index] = value;
    setAboutData(prev => ({
      ...prev,
      [field]: newArray
    }));
  };

  // 添加新项
  const addArrayItem = (field: 'values' | 'achievements') => {
    setAboutData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  // 删除项
  const removeArrayItem = (field: 'values' | 'achievements', index: number) => {
    const newArray = [...aboutData[field]];
    newArray.splice(index, 1);
    if (newArray.length === 0) {
      newArray.push('');
    }
    setAboutData(prev => ({
      ...prev,
      [field]: newArray
    }));
  };

  // 保存数据
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    
    try {
      const response = await fetch('/api/admin/about', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(aboutData),
      });
      
      if (!response.ok) {
        throw new Error('保存关于我们数据失败');
      }
      
      alert('保存成功！');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <div className="p-8 text-center">加载中...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">关于我们管理</h1>
      </div>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              公司名称
            </label>
            <input
              type="text"
              name="companyName"
              value={aboutData.companyName}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              成立年份
            </label>
            <input
              type="text"
              name="foundedYear"
              value={aboutData.foundedYear}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            公司使命
          </label>
          <textarea
            name="mission"
            value={aboutData.mission}
            onChange={handleChange}
            rows={3}
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            公司愿景
          </label>
          <textarea
            name="vision"
            value={aboutData.vision}
            onChange={handleChange}
            rows={3}
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            公司简介
          </label>
          <textarea
            name="description"
            value={aboutData.description}
            onChange={handleChange}
            rows={5}
            className="w-full p-2 border border-gray-300 rounded-md"
            required
          />
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            公司历史
          </label>
          <textarea
            name="history"
            value={aboutData.history}
            onChange={handleChange}
            rows={5}
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>
        
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium text-gray-700">
              公司价值观
            </label>
            <button
              type="button"
              onClick={() => addArrayItem('values')}
              className="text-blue-500 hover:text-blue-700"
            >
              + 添加价值观
            </button>
          </div>
          {aboutData.values.map((value, index) => (
            <div key={index} className="flex items-center mb-2">
              <input
                type="text"
                value={value}
                onChange={(e) => handleArrayChange('values', index, e.target.value)}
                className="flex-grow p-2 border border-gray-300 rounded-md mr-2"
              />
              <button
                type="button"
                onClick={() => removeArrayItem('values', index)}
                className="text-red-500 hover:text-red-700"
                disabled={aboutData.values.length === 1}
              >
                删除
              </button>
            </div>
          ))}
        </div>
        
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium text-gray-700">
              公司成就
            </label>
            <button
              type="button"
              onClick={() => addArrayItem('achievements')}
              className="text-blue-500 hover:text-blue-700"
            >
              + 添加成就
            </button>
          </div>
          {aboutData.achievements.map((achievement, index) => (
            <div key={index} className="flex items-center mb-2">
              <input
                type="text"
                value={achievement}
                onChange={(e) => handleArrayChange('achievements', index, e.target.value)}
                className="flex-grow p-2 border border-gray-300 rounded-md mr-2"
              />
              <button
                type="button"
                onClick={() => removeArrayItem('achievements', index)}
                className="text-red-500 hover:text-red-700"
                disabled={aboutData.achievements.length === 1}
              >
                删除
              </button>
            </div>
          ))}
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={saving}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:bg-blue-300"
          >
            {saving ? '保存中...' : '保存更改'}
          </button>
        </div>
      </form>
    </div>
  );
}