'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function ContactPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [contactData, setContactData] = useState({
    address: '',
    phone: '',
    email: '',
    workingHours: '',
    mapLocation: {
      lat: '',
      lng: '',
      mapType: null as 'leaflet' | null
    },
    socialMedia: {
      weibo: '',
      wechat: '',
      linkedin: '',
      facebook: '',
      twitter: '',
      instagram: '',
      youtube: '',
      tiktok: '',
      pinterest: ''
    }
  });

  // 获取联系我们的数据
  useEffect(() => {
    const fetchContactData = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/admin/contact');
        if (!response.ok) {
          throw new Error('获取联系我们数据失败');
        }
        const data = await response.json();
        setContactData(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchContactData();
  }, []);

  // 处理表单字段变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setContactData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 当地址变更且已选择地图类型时，尝试地理编码
    if (name === 'address' && contactData.mapLocation.mapType && value.trim().length > 5) {
      geocodeAddress(value);
    }
  };

  // 地理编码函数 - 将地址转换为经纬度
  const geocodeAddress = async (address: string) => {
    if (!address || address.trim().length < 3) {
      setError('请输入有效的地址进行定位');
      return;
    }
    
    try {
      // 显示加载状态
      setError('正在获取地址坐标...');
      
      // 尝试使用更完整的地址格式
      const fullAddress = `${address}, 中国`;
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(fullAddress)}&limit=1&countrycodes=cn&addressdetails=1`);
      
      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.length > 0) {
        const { lat, lon } = data[0];
        
        // 更新地图位置
        setContactData(prev => ({
          ...prev,
          mapLocation: {
            ...prev.mapLocation,
            lat: lat,
            lng: lon
          }
        }));
        
        // 清除错误信息
        setError(null);
      } else {
        setError('无法自动获取地址坐标，请使用下方提供的工具手动查询坐标');
      }
    } catch (error) {
      console.error('地理编码失败:', error);
      setError('地理编码失败，请使用下方提供的工具手动查询坐标');
    }
  };

  // 处理嵌套对象字段变化
  const handleNestedChange = (parent: 'mapLocation' | 'socialMedia', field: string, value: string) => {
    setContactData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }));
  };

  // 处理地图类型变更
  const handleMapTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const mapType = e.target.value as 'leaflet' | '';
    setContactData(prev => ({
      ...prev,
      mapLocation: {
        ...prev.mapLocation,
        mapType: mapType || null
      }
    }));
  };
  
  // 删除地图
  const handleRemoveMap = () => {
    setContactData(prev => ({
      ...prev,
      mapLocation: {
        lat: '',
        lng: '',
        mapType: null
      }
    }));
  };

  // 保存数据
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    
    try {
      const response = await fetch('/api/admin/contact', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contactData),
      });
      
      if (!response.ok) {
        throw new Error('保存联系我们数据失败');
      }
      
      alert('保存成功！');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <div className="p-8 text-center">加载中...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">联系方式管理</h1>
      </div>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            公司地址
          </label>
          <textarea
            name="address"
            value={contactData.address}
            onChange={handleChange}
            rows={3}
            className="w-full p-2 border border-gray-300 rounded-md"
            required
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              联系电话
            </label>
            <input
              type="text"
              name="phone"
              value={contactData.phone}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              电子邮箱
            </label>
            <input
              type="email"
              name="email"
              value={contactData.email}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              required
            />
          </div>
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            工作时间
          </label>
          <input
            type="text"
            name="workingHours"
            value={contactData.workingHours}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="例如：周一至周五 9:00-18:00"
          />
        </div>
        
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3">地图位置</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                地图类型
              </label>
              <select
                value={contactData.mapLocation.mapType || ''}
                onChange={handleMapTypeChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">不使用地图</option>
                <option value="leaflet">Leaflet地图</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                纬度
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={contactData.mapLocation.lat}
                  onChange={(e) => handleNestedChange('mapLocation', 'lat', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-l-md"
                  disabled={!contactData.mapLocation.mapType}
                />
                {contactData.mapLocation.mapType && (
                  <button
                    type="button"
                    onClick={() => {
                      if ("geolocation" in navigator) {
                        navigator.geolocation.getCurrentPosition(
                          (position) => {
                            handleNestedChange('mapLocation', 'lat', position.coords.latitude.toString());
                            handleNestedChange('mapLocation', 'lng', position.coords.longitude.toString());
                          },
                          (error) => {
                            setError('获取位置信息失败：' + error.message);
                          }
                        );
                      } else {
                        setError('您的浏览器不支持地理位置功能');
                      }
                    }}
                    className="bg-blue-500 text-white px-3 py-2 rounded-r-md hover:bg-blue-600"
                    title="获取当前位置"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                经度
              </label>
              <input
                type="text"
                value={contactData.mapLocation.lng}
                onChange={(e) => handleNestedChange('mapLocation', 'lng', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                disabled={!contactData.mapLocation.mapType}
              />
            </div>
          </div>
          
          {contactData.mapLocation.mapType && (
            <div className="mt-4 bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium">地图预览</h4>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => geocodeAddress(contactData.address)}
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                    disabled={!contactData.address}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    根据地址定位
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      if ("geolocation" in navigator) {
                        navigator.geolocation.getCurrentPosition(
                          (position) => {
                            handleNestedChange('mapLocation', 'lat', position.coords.latitude.toString());
                            handleNestedChange('mapLocation', 'lng', position.coords.longitude.toString());
                          },
                          (error) => {
                            setError('获取位置信息失败：' + error.message);
                          }
                        );
                      } else {
                        setError('您的浏览器不支持地理位置功能');
                      }
                    }}
                    className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    自动定位
                  </button>
                  <button
                    type="button"
                    onClick={handleRemoveMap}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    删除地图
                  </button>
                </div>
              </div>
              
              <div className="bg-gray-200 h-48 rounded-lg flex items-center justify-center">
                {contactData.mapLocation.lat && contactData.mapLocation.lng ? (
                  <div className="text-center">
                    <p className="mb-2">
                      {contactData.mapLocation.mapType === 'leaflet' && '使用Leaflet地图'}
                    </p>
                    <p>
                      位置: {contactData.mapLocation.lat}, {contactData.mapLocation.lng}
                    </p>
                    <p className="text-sm text-gray-500 mt-2">
                      (实际地图将在前台页面显示)
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-500">请输入有效的经纬度坐标</p>
                )}
              </div>
              
              {/* 在地图预览部分修改提示信息 */}
              <div className="mt-2 text-sm text-gray-600">
                <p>提示：</p>
                <ul className="list-disc pl-5">
                  <li>可以通过输入详细地址自动获取地图坐标（可能不支持所有中文地址）</li>
                  <li>如果自动定位失败，请使用以下工具手动查询坐标：
                    <a 
                      href="https://api.map.baidu.com/lbsapi/getpoint/index.html" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline ml-1"
                    >
                      百度坐标拾取器
                    </a>
                  </li>
                  <li>查询到坐标后，将纬度和经度分别复制到对应输入框</li>
                  <li>确保输入正确的经纬度格式</li>
                </ul>
              </div>
            </div>
          )}
        </div>
        
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3">社交媒体</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                微信公众号
              </label>
              <input
                type="text"
                value={contactData.socialMedia.wechat}
                onChange={(e) => handleNestedChange('socialMedia', 'wechat', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="公众号ID"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                LinkedIn
              </label>
              <input
                type="text"
                value={contactData.socialMedia.linkedin}
                onChange={(e) => handleNestedChange('socialMedia', 'linkedin', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="LinkedIn主页链接"
              />
            </div>
            
            {/* 国际社交媒体 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Facebook
              </label>
              <input
                type="text"
                value={contactData.socialMedia.facebook}
                onChange={(e) => handleNestedChange('socialMedia', 'facebook', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Facebook主页链接"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Twitter / X
              </label>
              <input
                type="text"
                value={contactData.socialMedia.twitter}
                onChange={(e) => handleNestedChange('socialMedia', 'twitter', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Twitter/X主页链接"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Instagram
              </label>
              <input
                type="text"
                value={contactData.socialMedia.instagram}
                onChange={(e) => handleNestedChange('socialMedia', 'instagram', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Instagram主页链接"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                YouTube
              </label>
              <input
                type="text"
                value={contactData.socialMedia.youtube}
                onChange={(e) => handleNestedChange('socialMedia', 'youtube', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="YouTube频道链接"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                TikTok
              </label>
              <input
                type="text"
                value={contactData.socialMedia.tiktok}
                onChange={(e) => handleNestedChange('socialMedia', 'tiktok', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="TikTok主页链接"
              />
            </div>
            
          </div>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={saving}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:bg-blue-300"
          >
            {saving ? '保存中...' : '保存更改'}
          </button>
        </div>
      </form>
    </div>
  );
}