import { NextAuthOptions } from 'next-auth'
import Credentials<PERSON>rovider from 'next-auth/providers/credentials'
import { DefaultSession } from 'next-auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// 扩展用户类型以包含ID、角色和手机号
declare module 'next-auth' {
  interface User {
    id?: string;
    role?: string;
    phone?: string;
  }
  interface Session {
    user: {
      id?: string;
      role?: string;
      phone?: string;
    } & DefaultSession['user'];
  }
}

// 扩展JWT类型
declare module 'next-auth/jwt' {
  interface JWT {
    id?: string;
    role?: string;
    phone?: string;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        console.log('收到登录请求:', credentials);

        if (!credentials?.username || !credentials?.password) {
          console.log('缺少用户名或密码');
          return null;
        }

        try {
          // 从数据库查找用户
          const user = await prisma.user.findFirst({
            where: {
              username: credentials.username
            }
          });

          if (!user) {
            console.log('用户不存在:', credentials.username);
            return null;
          }

          console.log('找到用户:', user.username);

          // 验证密码 - 使用 bcrypt 比较
          const isValid = await bcrypt.compare(credentials.password, user.password);
          console.log('密码验证结果:', isValid, '输入密码:', credentials.password, '正确密码:', user.password);

          if (!isValid) return null;

          // 返回用户信息（不包含密码）
          const userInfo = {
            id: String(user.id), // 确保ID是字符串类型
            name: user.username, // 使用username作为name
            email: user.email || '',
            phone: user.phone || '',
            role: user.role
          };

        console.log('登录成功，返回用户信息:', userInfo);
        return userInfo;
        } catch (error) {
          console.error('登录验证错误:', error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      // 首次登录时，将用户ID、角色和手机号添加到token
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.phone = user.phone;
      }
      return token;
    },
    async session({ session, token }) {
      // 将token中的ID、角色信息和手机号添加到session
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.phone = token.phone as string;
      }
      return session;
    }
  },
  pages: {
    signIn: '/admin/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24小时
  },
  secret: process.env.NEXTAUTH_SECRET || "qmw/qBozQQT2tCQ1ZU5K8pccGPjG6l2IlLIcpRq1GZM=",
}