'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

// 定义服务类型接口
interface ServiceType {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
  imageUrl?: string; // 添加imageUrl属性
}

export default function EditService({
  params
}: {
  params: { id: string }
}) {
  const router = useRouter();
  const { id } = params;
  
  const [service, setService] = useState<ServiceType>({
    id: '',
    title: '',
    description: '',
    icon: '',
    features: [''],
    imageUrl: '' // 初始化imageUrl属性
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 获取服务详情
  useEffect(() => {
    const fetchService = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/admin/services/${id}`);
        if (!response.ok) {
          throw new Error('获取服务详情失败');
        }
        const data = await response.json();
        setService(data.service);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchService();
  }, [id]);
  
  // 处理表单字段变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setService((prev: ServiceType) => ({
      ...prev,
      [name]: value
    }));
  };
  
  // 处理特性变化
  const handleFeatureChange = (index: number, value: string) => {
    const newFeatures = [...service.features];
    newFeatures[index] = value;
    setService((prev: ServiceType) => ({
      ...prev,
      features: newFeatures
    }));
  };
  
  // 添加新特性
  const addFeature = () => {
    setService((prev: ServiceType) => ({
      ...prev,
      features: [...prev.features, '']
    }));
  };
  
  // 删除特性
  const removeFeature = (index: number) => {
    const newFeatures = [...service.features];
    newFeatures.splice(index, 1);
    // 确保至少有一个空的特性输入框
    if (newFeatures.length === 0) {
      newFeatures.push('');
    }
    setService((prev: ServiceType) => ({
      ...prev,
      features: newFeatures
    }));
  };
  
  // 保存服务
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    
    try {
      const response = await fetch(`/api/admin/services/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(service),
      });
      
      if (!response.ok) {
        throw new Error('保存服务失败');
      }
      
      // 保存成功后跳转
      router.push('/admin/services');
    } catch (err: any) {
      setError(err.message);
      setSaving(false); // 只在出错时重置保存状态
    }
  };
  
  // 在获取服务详情失败时的处理
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600 mb-4"></div>
        <p className="ml-2 text-gray-600">加载中...</p>
      </div>
    );
  } else if (error && !service.id) {
    // 如果获取服务详情失败且没有服务ID，显示错误信息和返回按钮
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center flex-col">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 mb-4 flex items-center">
            <svg className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <span className="font-medium">获取服务详情失败</span>
          </div>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex justify-end">
            <Link
              href="/admin/services"
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              返回服务列表
            </Link>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <Link href="/admin/dashboard" className="text-xl font-bold text-gray-800">
                  MOCO 管理系统
                </Link>
              </div>
              <div className="ml-6 flex space-x-8">
                <Link href="/admin/dashboard" className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                  仪表盘
                </Link>
                <Link href="/admin/services" className="border-blue-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                  服务管理
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="py-10">
        <header>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 className="text-3xl font-bold text-gray-900">编辑服务</h1>
          </div>
        </header>
        <main>
          <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-8 sm:px-0">
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <form onSubmit={handleSubmit} className="p-6">
                  {error && (
                    <div className="mb-4 bg-red-50 border-l-4 border-red-500 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-red-700">{error}</p>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 gap-6">
                    <div>
                      <label htmlFor="id" className="block text-sm font-medium text-gray-700">
                        服务ID
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="id"
                          id="id"
                          value={service.id}
                          onChange={handleChange}
                          disabled
                          className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md bg-gray-100"
                        />
                      </div>
                      <p className="mt-1 text-sm text-gray-500">服务ID不可修改</p>
                    </div>
                    
                    <div>
                      <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                        服务名称
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="title"
                          id="title"
                          value={service.title}
                          onChange={handleChange}
                          required
                          className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                        服务描述
                      </label>
                      <div className="mt-1">
                        <textarea
                          name="description"
                          id="description"
                          rows={4}
                          value={service.description}
                          onChange={handleChange}
                          required
                          className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="icon" className="block text-sm font-medium text-gray-700">
                        图标名称
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="icon"
                          id="icon"
                          value={service.icon}
                          onChange={handleChange}
                          className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                      <p className="mt-1 text-sm text-gray-500">输入图标名称，例如：code, design, support</p>
                    </div>
                    
                    <div>
                      <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700">
                        图片URL
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="imageUrl"
                          id="imageUrl"
                          value={service.imageUrl}
                          onChange={handleChange}
                          className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                      <p className="mt-1 text-sm text-gray-500">输入图片的URL地址</p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        服务特性
                      </label>
                      <div className="mt-2 space-y-3">
                        {service.features.map((feature: string, index: number) => (
                          <div key={index} className="flex items-center">
                            <input
                              type="text"
                              value={feature}
                              onChange={(e) => handleFeatureChange(index, e.target.value)}
                              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            />
                            <button
                              type="button"
                              onClick={() => removeFeature(index)}
                              className="ml-2 inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        ))}
                        <button
                          type="button"
                          onClick={addFeature}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          添加特性
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 flex justify-end space-x-3">
                    <Link
                      href="/admin/services"
                      className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      取消
                    </Link>
                    <button
                      type="submit"
                      disabled={saving}
                      className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${saving ? 'opacity-70 cursor-not-allowed' : ''}`}
                    >
                      {saving ? '保存中...' : '保存'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}