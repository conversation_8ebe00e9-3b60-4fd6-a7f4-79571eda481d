'use client';

import { useState } from 'react';

export default function ServicesManagementPage() {
  const [services, setServices] = useState([
    { 
      id: 1, 
      name: '工程设计', 
      description: '提供专业的化工与石化工程设计服务，包括概念设计、基础设计和详细设计。',
      icon: 'design'
    },
    { 
      id: 2, 
      name: '设备采购', 
      description: '协助客户采购高质量的设备和材料，确保项目的质量和进度。',
      icon: 'procurement'
    },
    { 
      id: 3, 
      name: '工程施工', 
      description: '提供专业的施工服务，确保项目按时、按质、按量完成。',
      icon: 'construction'
    },
    { 
      id: 4, 
      name: '项目管理', 
      description: '全面的项目管理服务，确保项目在预算内按时完成。',
      icon: 'management'
    },
  ]);

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">服务管理</h1>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-semibold">服务列表</h2>
          <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            添加新服务
          </button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  服务名称
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  描述
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  图标
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {services.map((service) => (
                <tr key={service.id}>
                  <td className="px-6 py-4 whitespace-nowrap font-medium">
                    {service.name}
                  </td>
                  <td className="px-6 py-4">
                    <div className="line-clamp-2">{service.description}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {service.icon}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap space-x-2">
                    <button className="text-blue-500 hover:text-blue-700">
                      编辑
                    </button>
                    <button className="text-red-500 hover:text-red-700">
                      删除
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}