'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

// 定义服务类型接口
interface ServiceType {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
}

export default function NewService() {
  const router = useRouter();
  
  const [service, setService] = useState<ServiceType>({
    id: '',
    title: '',
    description: '',
    icon: '',
    features: ['']
  });
  
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 处理表单字段变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setService((prev: ServiceType) => ({
      ...prev,
      [name]: value
    }));
  };
  
  // 处理特性变化
  const handleFeatureChange = (index: number, value: string) => {
    const updatedFeatures = [...service.features];
    updatedFeatures[index] = value;
    setService((prev: ServiceType) => ({
      ...prev,
      features: updatedFeatures
    }));
  };
  
  // 添加新特性
  const addFeature = () => {
    setService((prev: ServiceType) => ({
      ...prev,
      features: [...prev.features, '']
    }));
  };
  
  // 删除特性
  const removeFeature = (index: number) => {
    const updatedFeatures = [...service.features];
    updatedFeatures.splice(index, 1);
    setService((prev: ServiceType) => ({
      ...prev,
      features: updatedFeatures
    }));
  };
  
  // 保存服务
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    
    try {
      // 过滤掉空的特性
      const filteredFeatures = service.features.filter(feature => feature.trim() !== '');
      const serviceToSave = {
        ...service,
        features: filteredFeatures
      };
      
      const response = await fetch('/api/admin/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(serviceToSave),
      });
      
      if (!response.ok) {
        throw new Error('创建服务失败');
      }
      
      router.push('/admin/services');
    } catch (err: any) {
      setError(err.message);
      setSaving(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <Link href="/admin/dashboard" className="text-xl font-bold text-gray-800">
                  MOCO 管理系统
                </Link>
              </div>
              <div className="ml-6 flex space-x-8">
                <Link href="/admin/dashboard" className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                  仪表盘
                </Link>
                <Link href="/admin/services" className="border-blue-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                  服务管理
                </Link>
                <Link href="/admin/projects" className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                  项目管理
                </Link>
                <Link href="/admin/content" className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                  内容管理
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="py-10">
        <header>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 className="text-3xl font-bold text-gray-900">添加新服务</h1>
          </div>
        </header>
        <main>
          <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div className="px-4 py-8 sm:px-0">
              <div className="bg-white shadow overflow-hidden sm:rounded-lg">
                <form onSubmit={handleSubmit} className="p-6">
                  {error && (
                    <div className="mb-4 bg-red-50 border-l-4 border-red-500 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-red-700">{error}</p>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 gap-6">
                    <div>
                      <label htmlFor="id" className="block text-sm font-medium text-gray-700">
                        服务ID
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="id"
                          id="id"
                          value={service.id}
                          onChange={handleChange}
                          required
                          className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                      <p className="mt-1 text-sm text-gray-500">服务的唯一标识符，例如：web-development, ui-design</p>
                    </div>
                    
                    <div>
                      <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                        服务名称
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="title"
                          id="title"
                          value={service.title}
                          onChange={handleChange}
                          required
                          className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="icon" className="block text-sm font-medium text-gray-700">
                        图标 (Emoji)
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="icon"
                          id="icon"
                          value={service.icon}
                          onChange={handleChange}
                          placeholder="例如: 💻, 🎨, 📱"
                          className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                      <p className="mt-1 text-sm text-gray-500">输入一个表示该服务的Emoji图标</p>
                    </div>
                    
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                        服务描述
                      </label>
                      <div className="mt-1">
                        <textarea
                          name="description"
                          id="description"
                          rows={3}
                          value={service.description}
                          onChange={handleChange}
                          required
                          className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        />
                      </div>
                      <p className="mt-1 text-sm text-gray-500">简短描述该服务的内容和价值</p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        服务特性
                      </label>
                      <p className="mt-1 text-sm text-gray-500">列出该服务的主要特点或优势</p>
                      
                      <div className="mt-2 space-y-2">
                        {service.features.map((feature, index) => (
                          <div key={index} className="flex items-center">
                            <input
                              type="text"
                              value={feature}
                              onChange={(e) => handleFeatureChange(index, e.target.value)}
                              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                              placeholder={`特性 ${index + 1}`}
                            />
                            {service.features.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removeFeature(index)}
                                className="ml-2 inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                              >
                                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            )}
                          </div>
                        ))}
                        
                        <button
                          type="button"
                          onClick={addFeature}
                          className="mt-2 inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                        >
                          <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          添加特性
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 flex justify-end space-x-3">
                    <Link
                      href="/admin/services"
                      className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      取消
                    </Link>
                    <button
                      type="submit"
                      disabled={saving}
                      className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${saving ? 'opacity-70 cursor-not-allowed' : ''}`}
                    >
                      {saving ? '保存中...' : '保存'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}