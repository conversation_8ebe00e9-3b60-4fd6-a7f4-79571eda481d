'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  // 判断是否是登录页面
  const isLoginPage = pathname === '/admin' || pathname === '/admin/login';

  useEffect(() => {
    // 检查是否已登录
    const checkAuth = () => {
      try {
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        if (!isLoggedIn && !isLoginPage) {
          router.push('/admin');
        } else if (isLoggedIn) {
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('检查登录状态时出错:', error);
        if (!isLoginPage) {
          router.push('/admin');
        }
      } finally {
        setIsLoading(false);
      }
    };
    
    checkAuth();
  }, [router, pathname, isLoginPage]);

  if (isLoading) {
    return <div className="p-8">加载中...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {!isLoginPage && isAuthenticated && (
        <nav className="bg-white shadow-md">
          <div className="container mx-auto px-6 py-3">
            <div className="flex justify-between items-center">
              <div className="text-xl font-bold">MOCO TECH 管理系统</div>
              <div className="flex space-x-4">
                <Link 
                  href="/admin/dashboard" 
                  className={`px-3 py-2 rounded hover:bg-gray-100 ${
                    pathname === '/admin/dashboard' ? 'bg-gray-100 font-medium' : ''
                  }`}
                >
                  仪表盘
                </Link>
                {/* 其他导航链接保持不变 */}
                <Link 
                  href="/admin/about" 
                  className={`px-3 py-2 rounded hover:bg-gray-100 ${
                    pathname?.includes('/admin/about') ? 'bg-gray-100 font-medium' : ''
                  }`}
                >
                  关于我们
                </Link>
                <Link 
                  href="/admin/services" 
                  className={`px-3 py-2 rounded hover:bg-gray-100 ${
                    pathname?.includes('/admin/services') ? 'bg-gray-100 font-medium' : ''
                  }`}
                >
                  服务管理
                </Link>
                <Link 
                  href="/admin/projects" 
                  className={`px-3 py-2 rounded hover:bg-gray-100 ${
                    pathname?.includes('/admin/projects') ? 'bg-gray-100 font-medium' : ''
                  }`}
                >
                  项目案例
                </Link>
                <Link 
                  href="/admin/contact" 
                  className={`px-3 py-2 rounded hover:bg-gray-100 ${
                    pathname?.includes('/admin/contact') ? 'bg-gray-100 font-medium' : ''
                  }`}
                >
                  联系方式
                </Link>
                <Link 
                  href="/zh" 
                  className="px-3 py-2 text-green-500 hover:bg-green-50 rounded"
                  target="_blank"
                >
                  返回网站
                </Link>
                <button 
                  onClick={() => {
                    localStorage.removeItem('admin_logged_in');
                    router.push('/admin');
                  }}
                  className="px-3 py-2 text-red-500 hover:bg-red-50 rounded"
                >
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </nav>
      )}
      <div className="container mx-auto p-6">
        {children}
      </div>
    </div>
  );
}