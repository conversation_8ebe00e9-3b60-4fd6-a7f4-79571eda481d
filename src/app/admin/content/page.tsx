'use client';

import { useState } from 'react';

export default function ContentManagementPage() {
  const [pages, setPages] = useState([
    { id: 1, title: '首页', path: '/', lastUpdated: '2023-05-15' },
    { id: 2, title: '关于我们', path: '/about', lastUpdated: '2023-05-10' },
    { id: 3, title: '产品介绍', path: '/products', lastUpdated: '2023-05-12' },
    { id: 4, title: '联系我们', path: '/contact', lastUpdated: '2023-05-08' },
  ]);

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">内容管理</h1>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-semibold">网站页面</h2>
          <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            添加新页面
          </button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  页面标题
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  路径
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  最后更新
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {pages.map((page) => (
                <tr key={page.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {page.title}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {page.path}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {page.lastUpdated}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap space-x-2">
                    <button className="text-blue-500 hover:text-blue-700">
                      编辑
                    </button>
                    <button className="text-red-500 hover:text-red-700">
                      删除
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}