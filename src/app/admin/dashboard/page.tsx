'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function DashboardPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // 检查是否已登录
    const checkAuth = () => {
      try {
        const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        if (!isLoggedIn) {
          router.push('/admin/login');
        } else {
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('检查登录状态时出错:', error);
        router.push('/admin/login');
      } finally {
        setIsLoading(false);
      }
    };
    
    checkAuth();
  }, [router]);

  const handleLogout = () => {
    try {
      localStorage.removeItem('admin_logged_in');
      router.push('/admin/login');
    } catch (error) {
      console.error('退出登录时出错:', error);
    }
  };

  if (isLoading) {
    return <div className="p-8">加载中...</div>;
  }

  if (!isAuthenticated) {
    return null; // 会被 useEffect 中的重定向处理
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto p-8">
        <h1 className="text-3xl font-bold mb-6">管理员仪表盘</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">网站统计</h2>
            <div className="space-y-2">
              <p>页面访问量: 1,234</p>
              <p>独立访客: 567</p>
              <p>平均停留时间: 2分30秒</p>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">内容管理</h2>
            <div className="space-y-2">
              <p><Link href="/admin/about" className="text-blue-500 hover:underline">关于我们</Link></p>
              <p><Link href="/admin/services" className="text-blue-500 hover:underline">服务内容管理</Link></p>
              <p><Link href="/admin/projects" className="text-blue-500 hover:underline">项目案例管理</Link></p>
              <p><Link href="/admin/contact" className="text-blue-500 hover:underline">联系方式管理</Link></p>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">系统设置</h2>
            <div className="space-y-2">
              <p><Link href="/admin/settings" className="text-blue-500 hover:underline">网站设置</Link></p>
              <p><Link href="/zh" className="text-green-500 hover:underline" target="_blank">访问公司网站</Link></p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">最近活动</h2>
          <div className="space-y-3">
            <div className="pb-2 border-b">
              <p className="text-sm text-gray-500">2023-05-15 14:30</p>
              <p>更新了工程设计服务内容</p>
            </div>
            <div className="pb-2 border-b">
              <p className="text-sm text-gray-500">2023-05-14 10:15</p>
              <p>添加了新项目案例：上海石化厂改造工程</p>
            </div>
            <div className="pb-2 border-b">
              <p className="text-sm text-gray-500">2023-05-13 16:45</p>
              <p>更新了联系我们页面地址信息</p>
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <button 
            onClick={handleLogout}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            退出登录
          </button>
        </div>
      </div>
    </div>
  );
}