'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface Project {
  id: number;
  title: string;
  category: string;
  description: string;
  location: string;
  year: string;
  imageUrl: string;
  status?: string;
}

export default function ProjectsAdminPage() {
  const router = useRouter();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // 移除语言选择，默认使用中文
  const locale = 'zh';

  // 获取项目列表
  useEffect(() => {
    const fetchProjects = async () => {
      setLoading(true);
      try {
        console.log('正在获取项目列表...');
        const response = await fetch(`/api/admin/projects?locale=${locale}&t=${new Date().getTime()}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error('获取项目列表失败:', errorData);
          throw new Error(errorData.error || '获取项目列表失败');
        }
        
        const data = await response.json();
        console.log('获取到的项目列表:', data);
        setProjects(data);
      } catch (err: any) {
        console.error('获取项目列表错误:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
    
    // 添加页面聚焦时刷新数据的功能
    const handleFocus = () => {
      console.log('页面获得焦点，刷新项目列表');
      fetchProjects();
    };
    
    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [locale]);

  // 添加刷新项目列表的函数
  const refreshProjects = () => {
    console.log('手动刷新项目列表');
    setLoading(true);
    fetch(`/api/admin/projects?locale=${locale}&t=${new Date().getTime()}`)
      .then(response => {
        if (!response.ok) {
          return response.json().then(errorData => {
            console.error('刷新项目列表失败:', errorData);
            throw new Error(errorData.error || '获取项目列表失败');
          });
        }
        return response.json();
      })
      .then(data => {
        console.log('刷新获取到的项目列表:', data);
        setProjects(data);
      })
      .catch(err => {
        console.error('刷新项目列表错误:', err);
        setError(err.message);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 删除项目
  const handleDelete = async (id: number) => {
    if (!confirm('确定要删除这个项目吗？')) return;
    
    try {
      const response = await fetch(`/api/admin/projects?id=${id}&locale=${locale}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('删除项目失败');
      }
      
      // 更新项目列表
      setProjects(projects.filter(project => project.id !== id));
    } catch (err: any) {
      setError(err.message);
    }
  };

  // 切换项目状态
  const handleToggleStatus = async (id: number, currentStatus: string) => {
    const newStatus = currentStatus === 'published' ? 'draft' : 'published';
    
    try {
      const response = await fetch(`/api/admin/projects`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          locale,
          status: newStatus
        }),
      });
      
      if (!response.ok) {
        throw new Error('更新项目状态失败');
      }
      
      // 更新项目列表
      setProjects(projects.map(project => 
        project.id === id ? { ...project, status: newStatus } : project
      ));
    } catch (err: any) {
      setError(err.message);
    }
  };

  if (loading) {
    return <div className="p-8 text-center">加载中...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">项目管理</h1>
        <div className="flex space-x-2">
          <button 
            onClick={refreshProjects}
            className="bg-blue-500 text-white px-4 py-1 rounded hover:bg-blue-600 mr-2"
          >
            刷新列表
          </button>
          <button 
            onClick={() => router.push('/admin/projects/new')}
            className="bg-green-500 text-white px-4 py-1 rounded hover:bg-green-600"
          >
            添加项目
          </button>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地点</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年份</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {projects.map((project) => (
              <tr key={project.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{project.id}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{project.title}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{project.category}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{project.location}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{project.year}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button 
                    onClick={() => handleToggleStatus(project.id, project.status || 'draft')}
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      project.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}
                  >
                    {project.status === 'published' ? '已发布' : '草稿'}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    onClick={() => router.push(`/admin/projects/edit/${project.id}`)}
                    className="text-indigo-600 hover:text-indigo-900 mr-4"
                  >
                    编辑
                  </button>
                  <button 
                    onClick={() => handleDelete(project.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    删除
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}