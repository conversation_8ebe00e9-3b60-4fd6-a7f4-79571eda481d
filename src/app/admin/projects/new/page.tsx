'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

export default function NewProjectPage() {
  const router = useRouter();
  const [project, setProject] = useState({
    title: '',
    category: '',
    description: '',
    location: '',
    year: new Date().getFullYear().toString(),
    imageUrl: '/images/projects/default.jpg',
    status: 'draft'
  });
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadStatus, setUploadStatus] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  // 默认使用中文
  const locale = 'zh';

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
  
    setSaving(true);
    setError(null); // 清除之前的错误
    
    try {
      console.log('提交项目数据:', project);
      
      const response = await fetch('/api/admin/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...project,
          locale
        }),
      });
  
      const result = await response.json();
      console.log('服务器响应:', result);
      
      if (!response.ok) {
        throw new Error(result.error || '创建项目失败');
      }
  
      console.log('创建项目成功:', result);
      
      // 添加延迟，确保数据已经保存到数据库
      alert('项目创建成功，即将返回项目列表');
      setTimeout(() => {
        // 使用 replace 而不是 push，防止用户返回到表单页面
        router.replace('/admin/projects');
      }, 1000);
    } catch (err: any) {
      console.error('创建项目错误:', err);
      setError(err.message || '创建项目时发生未知错误');
      setSaving(false);
    }
  };
  
  // 处理图片上传 - 保留这个更完善的版本
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    const formData = new FormData();
    formData.append('file', file);
    
    setUploadStatus('上传中...');
    setError(null); // 清除之前的错误
    
    try {
      console.log('开始上传图片:', file.name);
      
      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || '上传图片失败');
      }
      
      console.log('上传图片成功:', result);
      
      // 更新项目图片URL
      setProject({
        ...project,
        imageUrl: result.url
      });
      
      setUploadStatus('上传成功');
    } catch (err: any) {
      console.error('上传图片错误:', err);
      setError(err.message || '上传图片时发生未知错误');
      setUploadStatus('上传失败');
    }
  };

  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProject(prev => ({ ...prev, [name]: value }));
  };

  // 触发文件选择
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">新增项目</h1>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">标题</label>
            <input
              type="text"
              name="title"
              value={project.title}
              onChange={handleChange}
              required
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">分类</label>
            <select
              name="category"
              value={project.category}
              onChange={handleChange}
              required
              className="w-full p-2 border border-gray-300 rounded"
            >
              <option value="">选择分类</option>
              <option value="petrochemical">石化</option>
              <option value="chemical">化工</option>
              <option value="refinery">炼油</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">地点</label>
            <input
              type="text"
              name="location"
              value={project.location}
              onChange={handleChange}
              required
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">年份</label>
            <input
              type="text"
              name="year"
              value={project.year}
              onChange={handleChange}
              required
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">图片</label>
            <div className="flex flex-col space-y-2">
              {project.imageUrl && (
                <div className="relative h-40 w-full border rounded overflow-hidden">
                  <Image 
                    src={project.imageUrl} 
                    alt={project.title || '项目图片'}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                </div>
              )}
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  name="imageUrl"
                  value={project.imageUrl}
                  onChange={handleChange}
                  required
                  className="flex-1 p-2 border border-gray-300 rounded"
                />
                <button
                  type="button"
                  onClick={triggerFileInput}
                  className="px-3 py-2 bg-gray-200 rounded hover:bg-gray-300"
                >
                  上传图片
                </button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>
              {uploadStatus && (
                <p className={`text-sm ${uploadStatus === '上传成功' ? 'text-green-600' : uploadStatus === '上传中...' ? 'text-blue-600' : 'text-red-600'}`}>
                  {uploadStatus}
                </p>
              )}
            </div>
          </div>

          <div className="col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
            <textarea
              name="description"
              value={project.description}
              onChange={handleChange}
              required
              rows={4}
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
            <select
              name="status"
              value={project.status}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded"
            >
              <option value="draft">草稿</option>
              <option value="published">已发布</option>
            </select>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => router.push('/admin/projects')}
            className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
          >
            取消
          </button>
          <button
            type="submit"
            disabled={saving}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {saving ? '保存中...' : '保存'}
          </button>
        </div>
      </form>
    </div>
  );
}