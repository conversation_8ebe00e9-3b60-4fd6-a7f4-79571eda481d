'use client';

import { getLocale } from '../../i18n';
import PageLayout from '../../components/PageLayout';
import Link from 'next/link';

export default function Services({
  params
}: {
  params: { locale: string }
}) {
  // 获取当前语言
  const locale = params.locale;
  
  // 获取当前语言的内容
  const t = getLocale(locale);
  
  // 服务数据
  const services = t.services.items;
  
  return (
    <PageLayout locale={locale} t={t} currentPath="services">
      <h1 className="text-4xl font-bold mb-4 text-gray-900">{t.services.title}</h1>
      <p className="text-xl mb-12 text-gray-800">{t.services.description}</p>
      
      {/* 服务列表 */}
      <div className="space-y-16">
        {services.map((service: any, index: number) => (
          <div key={service.id} className={`flex flex-col ${index % 2 === 1 ? 'md:flex-row-reverse' : 'md:flex-row'} gap-8 items-center`}>
            <div className="w-full md:w-1/2 bg-gray-200 h-64 rounded-lg flex items-center justify-center">
              {/* 这里可以放服务相关的图片 */}
              <span className="text-gray-500 text-lg">{service.title} Image</span>
            </div>
            <div className="w-full md:w-1/2">
              <h2 className="text-2xl font-bold mb-4 text-gray-900">{service.title}</h2>
              <p className="text-gray-800 mb-6">{service.description}</p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {service.features.map((feature: string, idx: number) => (
                  <div key={idx} className="flex items-center bg-white p-3 rounded-lg shadow-sm border border-gray-100">
                    <svg className="h-6 w-6 text-blue-600 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="ml-3 text-gray-900 font-medium">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* 服务咨询 */}
      <div className="mt-16 bg-blue-50 p-8 rounded-lg shadow-md text-center">
        <h2 className="text-2xl font-bold mb-4 text-gray-900">
          {t.services.consultation.title}
        </h2>
        <p className="text-gray-800 mb-6">
          {t.services.consultation.description}
        </p>
        <Link href={`/${locale}/contact`} className="inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-300">
          {t.services.consultation.button}
        </Link>
      </div>
    </PageLayout>
  );
}