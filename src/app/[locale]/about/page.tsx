'use client';

import { useEffect, useState } from 'react';
import PageLayout from '../../components/PageLayout';
import { getLocale } from '../../i18n';
import Image from 'next/image';

interface AboutData {
  companyName: string;
  foundedYear: string;
  mission: string;
  vision: string;
  description: string;
  history: string;
  values: string[];
  achievements: string[];
}

export default function AboutPage({
  params
}: {
  params: { locale: string }
}) {
  const locale = params.locale;
  const t = getLocale(locale);
  
  const [aboutData, setAboutData] = useState<AboutData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchAboutData = async () => {
      try {
        const response = await fetch('/api/admin/about');
        if (response.ok) {
          const data = await response.json();
          setAboutData(data);
        } else {
          throw new Error('获取关于我们数据失败');
        }
      } catch (error) {
        console.error('获取关于我们数据失败:', error);
        setError(locale === 'zh' ? '获取公司信息失败，请稍后重试' : 
                 locale === 'en' ? 'Failed to fetch company information, please try again later' : 
                 'فشل في جلب معلومات الشركة، يرجى المحاولة مرة أخرى لاحقًا');
      } finally {
        setLoading(false);
      }
    };
    
    fetchAboutData();
  }, [locale]);
  
  return (
    <PageLayout locale={locale} t={t} currentPath="about">
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-4xl font-bold mb-8 text-center">
          {locale === 'zh' ? '关于我们' : 
           locale === 'en' ? 'About Us' : 
           'معلومات عنا'}
        </h1>
        
        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-xl text-gray-600">
              {locale === 'zh' ? '加载中...' : 
               locale === 'en' ? 'Loading...' : 
               'جار التحميل...'}
            </p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="inline-block bg-red-100 p-4 rounded-lg mb-4">
              <svg className="h-12 w-12 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-xl text-gray-800">{error}</p>
          </div>
        ) : aboutData ? (
          <>
            {/* 公司简介 */}
            <section className="mb-16">
              <div className="flex flex-col md:flex-row items-center gap-8">
                <div className="md:w-1/2">
                  <h2 className="text-2xl font-bold mb-4">
                    {locale === 'zh' ? '公司概况' : 
                     locale === 'en' ? 'Company Overview' : 
                     'نظرة عامة على الشركة'}
                  </h2>
                  <p className="text-gray-700 mb-4">{aboutData.description}</p>
                  <p className="text-gray-700">
                    {locale === 'zh' ? '成立于' : 
                     locale === 'en' ? 'Founded in' : 
                     'تأسست في'} {aboutData.foundedYear}
                  </p>
                </div>
                <div className="md:w-1/2">
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-lg">
                    <Image
                      src="/images/about-company.jpg"
                      alt={aboutData.companyName}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              </div>
            </section>
            
            {/* 使命和愿景 */}
            <section className="mb-16 bg-gray-50 p-8 rounded-lg">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h2 className="text-2xl font-bold mb-4">
                    {locale === 'zh' ? '我们的使命' : 
                     locale === 'en' ? 'Our Mission' : 
                     'مهمتنا'}
                  </h2>
                  <p className="text-gray-700">{aboutData.mission}</p>
                </div>
                <div>
                  <h2 className="text-2xl font-bold mb-4">
                    {locale === 'zh' ? '我们的愿景' : 
                     locale === 'en' ? 'Our Vision' : 
                     'رؤيتنا'}
                  </h2>
                  <p className="text-gray-700">{aboutData.vision}</p>
                </div>
              </div>
            </section>
            
            {/* 公司价值观 */}
            <section className="mb-16">
              <h2 className="text-2xl font-bold mb-6 text-center">
                {locale === 'zh' ? '核心价值观' : 
                 locale === 'en' ? 'Core Values' : 
                 'القيم الأساسية'}
              </h2>
              <div className="grid md:grid-cols-3 gap-6">
                {aboutData.values.map((value, index) => (
                  <div key={index} className="bg-white p-6 rounded-lg shadow-md text-center">
                    <div className="text-blue-600 text-3xl mb-4">
                      {index === 0 ? '★' : index === 1 ? '✓' : '♥'}
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{value}</h3>
                  </div>
                ))}
              </div>
            </section>
            
            {/* 公司历史 */}
            {aboutData.history && (
              <section className="mb-16">
                <h2 className="text-2xl font-bold mb-6">
                  {locale === 'zh' ? '公司历史' : 
                   locale === 'en' ? 'Company History' : 
                   'تاريخ الشركة'}
                </h2>
                <div className="bg-white p-6 rounded-lg shadow-md">
                  <p className="text-gray-700">{aboutData.history}</p>
                </div>
              </section>
            )}
            
            {/* 公司成就 */}
            <section className="mb-16">
              <h2 className="text-2xl font-bold mb-6">
                {locale === 'zh' ? '公司成就' : 
                 locale === 'en' ? 'Achievements' : 
                 'إنجازات الشركة'}
              </h2>
              <div className="bg-blue-50 p-6 rounded-lg">
                <ul className="space-y-4">
                  {aboutData.achievements.map((achievement, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-blue-600 mr-2">✓</span>
                      <span>{achievement}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </section>
          </>
        ) : (
          <div className="text-center py-12 text-gray-500">
            {locale === 'zh' ? '暂无公司信息' : 
             locale === 'en' ? 'Company information not available' : 
             'معلومات الشركة غير متوفرة'}
          </div>
        )}
      </div>
    </PageLayout>
  );
}