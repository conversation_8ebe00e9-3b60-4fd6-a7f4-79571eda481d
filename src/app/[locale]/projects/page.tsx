'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getLocale } from '../../i18n';
import PageLayout from '../../components/PageLayout';

export default function Projects({
  params
}: {
  params: { locale: string }
}) {
  // 获取当前语言
  const locale = params.locale;
  
  // 获取当前语言的内容
  const t = getLocale(locale);
  
  // 添加状态控制项目筛选
  const [activeFilter, setActiveFilter] = useState('all');
  
  // 添加状态存储从后台获取的项目数据
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 从后台API获取项目数据
  const fetchProjects = async () => {
    setLoading(true);
    setError(null); // 重置错误状态
    try {
      // 模拟API请求
      // 在实际项目中，这里应该替换为真实的API调用
      const response = await fetch(`/api/projects?locale=${locale}`);
      if (!response.ok) {
        throw new Error('获取项目数据失败');
      }
      const data = await response.json();
      setProjects(data);
    } catch (error) {
      console.error('获取项目数据失败:', error);
      // 使用硬编码的错误信息，而不是依赖语言文件中可能不存在的键
      setError(locale === 'zh' ? '获取项目数据失败，请稍后重试' : 
               locale === 'en' ? 'Failed to fetch projects, please try again later' : 
               'فشل في جلب المشاريع، يرجى المحاولة مرة أخرى لاحقًا'); // 设置错误信息
      setProjects([]); // 清空项目数据
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchProjects();
  }, [locale]);
  
  // 根据筛选条件过滤项目
  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);
  
  return (
    <PageLayout locale={locale} t={t} currentPath="projects">
      <h1 className="text-4xl font-bold mb-4 text-gray-900">{t.projects.title}</h1>
      <p className="text-xl mb-8 text-gray-800">{t.projects.description}</p>
      
      {/* 项目筛选 */}
      <div className="flex flex-wrap gap-4 mb-8">
        <button 
          onClick={() => setActiveFilter('all')} 
          className={`px-4 py-2 rounded-md ${activeFilter === 'all' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300'}`}
        >
          {t.projects.filters.all}
        </button>
        <button 
          onClick={() => setActiveFilter('chemical')} 
          className={`px-4 py-2 rounded-md ${activeFilter === 'chemical' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300'}`}
        >
          {t.projects.filters.chemical}
        </button>
        <button 
          onClick={() => setActiveFilter('petrochemical')} 
          className={`px-4 py-2 rounded-md ${activeFilter === 'petrochemical' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300'}`}
        >
          {t.projects.filters.petrochemical}
        </button>
        <button 
          onClick={() => setActiveFilter('refinery')} 
          className={`px-4 py-2 rounded-md ${activeFilter === 'refinery' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300'}`}
        >
          {t.projects.filters.refinery}
        </button>
      </div>
      
      {/* 加载状态、错误状态和项目列表 */}
      {loading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-xl text-gray-600">{t.projects.loading}</p>
        </div>
      ) : error ? (
        // 错误状态显示
        <div className="text-center py-12">
          <div className="inline-block bg-red-100 p-4 rounded-lg mb-4">
            <svg className="h-12 w-12 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-xl text-gray-800">{error}</p>
          <button 
            onClick={() => fetchProjects()} 
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            {locale === 'zh' ? '重试' : 
             locale === 'en' ? 'Retry' : 
             'إعادة المحاولة'}
          </button>
        </div>
      ) : (
        <>
          {/* 项目列表 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project) => (
              <div key={project.id} className="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
                <div className="h-48 bg-gray-200 flex items-center justify-center overflow-hidden">
                  {project.imageUrl ? (
                    <Image 
                      src={project.imageUrl} 
                      alt={project.title} 
                      width={400}
                      height={300}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full w-full bg-gray-100">
                      <svg className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2 text-gray-900">{project.title}</h3>
                  <p className="text-gray-700 mb-4">{project.description}</p>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>{project.location}</span>
                    <span>{project.year}</span>
                  </div>
                  <div className="mt-4">
                    <Link href={`/${locale}/projects/${project.id}`} className="text-blue-600 hover:text-blue-800 font-medium">
                      {t.projects.viewDetails} →
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
            
            {/* 如果没有匹配的项目 */}
            {filteredProjects.length === 0 && (
              <div className="text-center py-12">
                <p className="text-xl text-gray-600">{t.projects.noProjects}</p>
              </div>
            )}
          </>
        )}
    </PageLayout>
  );
}