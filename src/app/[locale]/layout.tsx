import { NextIntlClientProvider } from 'next-intl'
import '../globals.css'

async function getMessages(locale: string) {
  try {
    return (await import(`../../../messages/${locale}.json`)).default
  } catch (error) {
    return {}
  }
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: { locale: string }
}) {
  // 使用await解包params
  const locale = params.locale;
  const messages = await getMessages(locale)
  
  // 为阿拉伯语设置从右到左的方向
  const direction = locale === 'ar' ? 'rtl' : 'ltr';

  return (
    <html lang={locale} dir={direction} suppressHydrationWarning={true}>
      <body suppressHydrationWarning={true} className={locale === 'ar' ? 'rtl' : 'ltr'}>
        <NextIntlClientProvider locale={locale} messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
