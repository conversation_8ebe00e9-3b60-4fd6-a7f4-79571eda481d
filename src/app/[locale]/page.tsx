'use client';

import { getLocale } from '../i18n';
import PageLayout from '../components/PageLayout';

export default function Home({
  params
}: {
  params: { locale: string }
}) {
  // 直接使用params.locale，因为在客户端组件中不需要await
  const locale = params.locale;
  const t = getLocale(locale);
  
  return (
    <PageLayout locale={locale} t={t} currentPath="">
      <h1 className="text-4xl font-bold mb-4 text-gray-900">{t.home.title}</h1>
      <p className="text-xl mb-8 text-gray-800">{t.home.description}</p>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
        {t.home.services.map((service: any) => (
          <div key={service.id} className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-2 text-gray-900">{service.title}</h3>
            <p className="text-gray-800">{service.description}</p>
          </div>
        ))}
      </div>
    </PageLayout>
  );
}
