'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import PageLayout from '../../components/PageLayout';
import { getLocale } from '../../i18n';

// 动态导入 Leaflet 地图组件，禁用 SSR
const LeafletMap = dynamic(
  () => import('../../components/LeafletMap'),
  { 
    ssr: false,
    loading: () => (
      <div className="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
        <p className="text-gray-500">地图加载中...</p>
      </div>
    )
  }
);

// 地图组件
const MapComponent = ({ mapType, lat, lng }: { mapType: string | null, lat: string, lng: string }) => {
  if (!mapType || !lat || !lng) return null;
  
  // 使用 Leaflet 地图
  if (mapType === 'leaflet') {
    // 确保经纬度是有效的数字
    const latitude = parseFloat(lat);
    const longitude = parseFloat(lng);
    
    if (isNaN(latitude) || isNaN(longitude)) {
      return (
        <div className="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
          <p className="text-gray-500">无效的地图坐标</p>
        </div>
      );
    }
    
    return <LeafletMap lat={latitude} lng={longitude} />;
  }
  
  return (
    <div className="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
      <p className="text-gray-500">地图不可用</p>
    </div>
  );
};

interface ContactData {
  address: string;
  phone: string;
  email: string;
  workingHours: string;
  mapLocation: {
    lat: string;
    lng: string;
    mapType: 'leaflet' | null;
  };
  socialMedia: {
    wechat: string;
    linkedin: string;
    facebook: string;
    twitter: string;
    instagram: string;
    youtube: string;
    tiktok: string;
  };
}

export default function ContactPage({
  params
}: {
  params: { locale: string }
}) {
  const locale = params.locale;
  const t = getLocale(locale);
  
  const [contactData, setContactData] = useState<ContactData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [formStatus, setFormStatus] = useState<{
    submitted: boolean;
    success: boolean;
    message: string;
  } | null>(null);
  
  useEffect(() => {
    const fetchContactData = async () => {
      try {
        const response = await fetch('/api/admin/contact');
        if (response.ok) {
          const data = await response.json();
          setContactData(data);
        } else {
          throw new Error('获取联系我们数据失败');
        }
      } catch (error) {
        console.error('获取联系我们数据失败:', error);
        setError(locale === 'zh' ? '获取联系方式失败，请稍后重试' : 
                 locale === 'en' ? 'Failed to fetch contact information, please try again later' : 
                 'فشل في جلب معلومات الاتصال، يرجى المحاولة مرة أخرى لاحقًا');
      } finally {
        setLoading(false);
      }
    };
    
    fetchContactData();
  }, [locale]);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // 这里可以添加发送表单数据到后端的逻辑
      // const response = await fetch('/api/contact', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(formData),
      // });
      
      // 模拟成功提交
      setFormStatus({
        submitted: true,
        success: true,
        message: locale === 'zh' ? '消息发送成功！我们会尽快回复您。' :
                 locale === 'en' ? 'Message sent successfully! We will get back to you soon.' :
                 'تم إرسال الرسالة بنجاح! سنرد عليك قريبًا.'
      });
      
      // 清空表单
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      setFormStatus({
        submitted: true,
        success: false,
        message: locale === 'zh' ? '发送失败，请稍后再试。' :
                 locale === 'en' ? 'Failed to send message. Please try again later.' :
                 'فشل في إرسال الرسالة. يرجى المحاولة مرة أخرى لاحقًا.'
      });
    }
  };
  
  return (
    <PageLayout locale={locale} t={t} currentPath="contact">
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-4xl font-bold mb-8 text-center">
          {locale === 'zh' ? '联系我们' : 
           locale === 'en' ? 'Contact Us' : 
           'اتصل بنا'}
        </h1>
        
        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-xl text-gray-600">
              {locale === 'zh' ? '加载中...' : 
               locale === 'en' ? 'Loading...' : 
               'جار التحميل...'}
            </p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="inline-block bg-red-100 p-4 rounded-lg mb-4">
              <svg className="h-12 w-12 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-xl text-gray-800">{error}</p>
          </div>
        ) : contactData ? (
          <div className="grid md:grid-cols-2 gap-12">
            {/* 联系信息 */}
            <div>
              <h2 className="text-2xl font-bold mb-6">
                {locale === 'zh' ? '联系方式' : 
                 locale === 'en' ? 'Contact Information' : 
                 'معلومات الاتصال'}
              </h2>
              
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="text-blue-600 mr-4 mt-1">
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1">
                      {locale === 'zh' ? '地址' : 
                       locale === 'en' ? 'Address' : 
                       'العنوان'}
                    </h3>
                    <p className="text-gray-700">{contactData.address}</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="text-blue-600 mr-4 mt-1">
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1">
                      {locale === 'zh' ? '电话' : 
                       locale === 'en' ? 'Phone' : 
                       'الهاتف'}
                    </h3>
                    <p className="text-gray-700">{contactData.phone}</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="text-blue-600 mr-4 mt-1">
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1">
                      {locale === 'zh' ? '邮箱' : 
                       locale === 'en' ? 'Email' : 
                       'البريد الإلكتروني'}
                    </h3>
                    <p className="text-gray-700">{contactData.email}</p>
                  </div>
                </div>
                
                {contactData.workingHours && (
                  <div className="flex items-start">
                    <div className="text-blue-600 mr-4 mt-1">
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-1">
                        {locale === 'zh' ? '工作时间' : 
                         locale === 'en' ? 'Working Hours' : 
                         'ساعات العمل'}
                      </h3>
                      <p className="text-gray-700">{contactData.workingHours}</p>
                    </div>
                  </div>
                )}
              </div>
              
              {/* 社交媒体 */}
              {Object.values(contactData.socialMedia).some(value => value) && (
                <div className="mt-8">
                  <h3 className="text-lg font-semibold mb-4">
                    {locale === 'zh' ? '关注我们' : 
                     locale === 'en' ? 'Follow Us' : 
                     'تابعنا'}
                  </h3>
                  <div className="flex flex-wrap gap-4">
                    {contactData.socialMedia.wechat && (
                      <a href="#" className="text-blue-600 hover:text-blue-800" title={contactData.socialMedia.wechat}>
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8.69,11.52c-0.59,0-1.21,0.54-1.21,1.21c0,0.66,0.62,1.21,1.21,1.21s1.21-0.54,1.21-1.21C9.9,12.06,9.28,11.52,8.69,11.52z M12,0C5.37,0,0,5.37,0,12c0,6.63,5.37,12,12,12c6.63,0,12-5.37,12-12C24,5.37,18.63,0,12,0z M16.54,16.3c-0.71,0.53-1.62,0.9-2.57,1.05c-0.95,0.15-1.95,0.07-2.86-0.23c-0.91-0.3-1.73-0.84-2.36-1.54c-0.63-0.69-1.07-1.53-1.24-2.42c-0.18-0.89-0.07-1.84,0.29-2.67c0.36-0.83,0.98-1.55,1.74-2.09c0.77-0.54,1.68-0.9,2.63-1.04c0.95-0.15,1.95-0.07,2.86,0.23c0.91,0.3,1.72,0.84,2.36,1.54c0.63,0.69,1.07,1.53,1.24,2.42c0.18,0.89,0.07,1.84-0.29,2.67C17.92,15.05,17.3,15.77,16.54,16.3z M15.31,11.52c-0.59,0-1.21,0.54-1.21,1.21c0,0.66,0.62,1.21,1.21,1.21c0.59,0,1.21-0.54,1.21-1.21C16.52,12.06,15.9,11.52,15.31,11.52z"></path>
                        </svg>
                      </a>
                    )}
                    {contactData.socialMedia.linkedin && (
                      <a href={contactData.socialMedia.linkedin} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                        </svg>
                      </a>
                    )}
                    {contactData.socialMedia.facebook && (
                      <a href={contactData.socialMedia.facebook} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385h-3.047v-3.47h3.047v-2.642c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953h-1.514c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385c5.738-.9 10.126-5.864 10.126-11.854z"/>
                        </svg>
                      </a>
                    )}
                    {contactData.socialMedia.twitter && (
                      <a href={contactData.socialMedia.twitter} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723 10.054 10.054 0 01-3.127 1.184 4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                      </a>
                    )}
                    {contactData.socialMedia.instagram && (
                      <a href={contactData.socialMedia.instagram} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z"/>
                        </svg>
                      </a>
                    )}
                    {contactData.socialMedia.youtube && (
                      <a href={contactData.socialMedia.youtube} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                        </svg>
                      </a>
                    )}
                    {contactData.socialMedia.tiktok && (
                      <a href={contactData.socialMedia.tiktok} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                        </svg>
                      </a>
                    )}
                  </div>
                </div>
              )}
              
              {/* 地图 */}
              {contactData.mapLocation && contactData.mapLocation.mapType && contactData.mapLocation.lat && contactData.mapLocation.lng && (
                <div className="mt-8">
                  <h3 className="text-lg font-semibold mb-4">
                    {locale === 'zh' ? '在地图上找到我们' : 
                     locale === 'en' ? 'Find Us on Map' : 
                     'جدنا على الخريطة'}
                  </h3>
                  <MapComponent 
                    mapType={contactData.mapLocation.mapType} 
                    lat={contactData.mapLocation.lat} 
                    lng={contactData.mapLocation.lng} 
                  />
                </div>
              )}
            </div>
            
            {/* 联系表单 */}
            <div>
              <h2 className="text-2xl font-bold mb-6">
                {locale === 'zh' ? '发送消息' : 
                 locale === 'en' ? 'Send Message' : 
                 'إرسال رسالة'}
              </h2>
              
              {formStatus && (
                <div className={`mb-6 p-4 rounded-lg ${formStatus.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                  {formStatus.message}
                </div>
              )}
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'zh' ? '姓名' : 
                     locale === 'en' ? 'Name' : 
                     'الاسم'} *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'zh' ? '邮箱' : 
                     locale === 'en' ? 'Email' : 
                     'البريد الإلكتروني'} *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'zh' ? '电话' : 
                     locale === 'en' ? 'Phone' : 
                     'الهاتف'}
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'zh' ? '主题' : 
                     locale === 'en' ? 'Subject' : 
                     'الموضوع'} *
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                    {locale === 'zh' ? '消息内容' : 
                     locale === 'en' ? 'Message' : 
                     'الرسالة'} *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={5}
                    required
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  ></textarea>
                </div>
                
                <div>
                  <button
                    type="submit"
                    className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition duration-300"
                  >
                    {locale === 'zh' ? '发送消息' : 
                     locale === 'en' ? 'Send Message' : 
                     'إرسال رسالة'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            {locale === 'zh' ? '暂无联系方式信息' : 
             locale === 'en' ? 'Contact information not available' : 
             'معلومات الاتصال غير متوفرة'}
          </div>
        )}
      </div>
    </PageLayout>
  );
}