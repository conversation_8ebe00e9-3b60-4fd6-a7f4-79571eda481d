#!/usr/bin/expect -f

set timeout 300
set server "47.236.247.50"
set user "root"
set password "Clyp20013517!"
set remote_dir "/var/www/companywebsite"

# 创建源代码包
spawn bash -c "tar -czf deploy-src.tar.gz src public prisma messages package.json package-lock.json next.config.js tsconfig.json postcss.config.js tailwind.config.js middleware.ts i18n.config.ts next-env.d.ts eslint.config.mjs .env.local"
expect eof

# 上传到服务器
spawn scp deploy-src.tar.gz $user@$server:/tmp/
expect {
    "yes/no" { send "yes\r"; exp_continue }
    "password:" { send "$password\r" }
}
expect eof

# 在服务器上执行更新
spawn ssh $user@$server
expect {
    "yes/no" { send "yes\r"; exp_continue }
    "password:" { send "$password\r" }
}
expect "#"

# 解压源代码包
send "cd $remote_dir && tar -xzf /tmp/deploy-src.tar.gz\r"
expect "#"

# 安装依赖
send "npm install\r"
expect "#"

# 生成Prisma客户端
send "npx prisma generate\r"
expect "#"

# 构建项目
send "npm run build\r"
expect "#"

# 重启应用并更新环境变量
send "pm2 restart companywebsite --update-env\r"
expect "#"

# 清理临时文件
send "rm -f /tmp/deploy-src.tar.gz\r"
expect "#"

# 退出SSH
send "exit\r"
expect eof

# 清理本地文件
spawn rm -f deploy-src.tar.gz
expect eof
